{"env": {}, "permissions": {"allow": ["Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(npm test)", "Bash(npm test:*)", "<PERSON><PERSON>(sed:*)", "mcp__ide__getDiagnostics", "Bash(go build:*)", "Bash(npx vite:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm start)", "<PERSON><PERSON>(go doc:*)", "<PERSON><PERSON>(timeout 300 npm test)", "Bash(sqlc:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(echo $?)", "Bash(npm run test:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mv:*)", "Bash(go mod:*)", "Ba<PERSON>(go vet:*)", "<PERSON><PERSON>(true)", "Bash(npm test -- --grep \"can change player 2 score on a match\" --project=app-chromium)", "mcp__playwright__browser_install", "mcp__playwright__browser_navigate", "Bash(air)", "Bash(air:*)", "Bash(npm run build:*)", "Bash(npm install)", "<PERSON><PERSON>(npx playwright install:*)", "mcp__playwright__browser_click", "mcp__playwright__browser_type", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_take_screenshot", "WebFetch(domain:htmx.org)", "Bash(npm install:*)", "Bash(npm test -- tests/e2e/app/subscription-upgrade.spec.js --grep \"User can successfully upgrade from free to pro\")", "Bash(./start_stripe_webhook.sh:*)", "Bash(stripe config:*)", "mcp__playwright__browser_network_requests", "WebFetch(domain:github.com)", "Bash(go fmt:*)", "<PERSON><PERSON>(go run:*)", "Bash(rm:*)", "<PERSON><PERSON>(go clean:*)", "Bash(stripe api versions:*)", "Bash(stripe listen:*)", "Bash(stripe events:*)", "Bash(stripe trigger payment_intent.succeeded)", "<PERSON><PERSON>(journalctl:*)", "Bash(/var/home/<USER>/.linuxbrew/Cellar/ripgrep/14.1.1/bin/rg -n -A5 -B5 \"SSEManager|SendToUser|SendToAll|Broadcast\" /var/home/<USER>/coachpad/handlers/settings.go)", "<PERSON><PERSON>(env)", "Bash(stripe webhooks:*)", "Bash(stripe webhook_endpoints:*)", "Bash(ps:*)", "<PERSON><PERSON>(podman exec:*)", "Bash(gofmt:*)", "Bash(kill:*)", "WebFetch(domain:echo.labstack.com)", "Bash(go get:*)", "<PERSON><PERSON>(timeout:*)", "Bash(go list:*)", "Bash(awk:*)", "<PERSON><PERSON>(python3:*)", "WebFetch(domain:heroicons.com)", "Bash(cp:*)", "Bash(npm run:*)", "Bash(node:*)", "mcp__playwright__browser_close", "WebFetch(domain:localhost)", "mcp__browsermcp__browser_screenshot", "mcp__browsermcp__browser_navigate", "mcp__browsermcp__browser_click", "mcp__browsermcp__browser_press_key", "mcp__browsermcp__browser_type", "mcp__browsermcp__browser_snapshot", "mcp__browsermcp__browser_get_console_logs", "<PERSON><PERSON>(goimports:*)", "Bash(for i in {1..3})", "Bash(do echo \"=== Test Run $i ===\")", "Bash(done)", "Bash(for i in {1..5})", "<PERSON><PERSON>(break)", "<PERSON><PERSON>(pkill:*)", "Bash(do echo \"=== RUN $i ===\")", "<PERSON><PERSON>(go test:*)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(chmod:*)", "Bash(for i in {6..10})", "mcp__browsermcp__browser_wait", "<PERSON><PERSON>(touch:*)", "mcp__ide__executeCode", "Bash(for i in {1..4})", "Bash(do echo \"--- Execution $i ---\")", "<PERSON><PERSON>(time npm test -- --grep \"user can see the details of a season including matches\")", "Bash(time npm test -- --grep \"user can delete a season successfully\")", "WebFetch(domain:playwright.dev)", "Bash(npx tsc:*)"], "deny": []}}