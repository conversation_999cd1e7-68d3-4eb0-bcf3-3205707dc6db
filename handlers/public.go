package handlers

import (
	"net/http"
	"strconv"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/templates/public"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
)

type PublicHandler struct {
	Queries *db.Queries
}

func (h *PublicHandler) RegisterRoutes(e *echo.Echo) {
	// Public schedule routes - no authentication required
	e.GET("/public/seasons/:seasonId", h.GetPublicSeasonSchedule)
	e.GET("/public/seasons/:seasonId/scoreboard", h.GetPublicSeasonScoreboard)
	e.GET("/public/schedule", h.GetPublicScheduleIndex)
	e.GET("/public/scoreboard", h.GetPublicScoreboardIndex)
}

// GetPublicSeasonSchedule serves the public schedule for a specific season
func (h *PublicHandler) GetPublicSeasonSchedule(c echo.Context) error {
	seasonIDStr := c.Param("seasonId")
	seasonID, err := strconv.ParseInt(seasonIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid season ID")
	}

	seasonID32 := int32(seasonID)

	// Get season without requiring user authentication
	season, err := h.Queries.GetSeasonPublic(c.Request().Context(), seasonID32)
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "Season not found")
	}

	// Get matches for the season
	seasonIDpg := pgtype.Int4{Int32: seasonID32, Valid: true}
	matches, err := h.Queries.GetMatchesBySeasonId(c.Request().Context(), seasonIDpg)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get matches")
	}

	// Get custom match columns
	customColumns, err := h.Queries.GetCustomMatchColumnsByUserID(c.Request().Context(), season.UserID.Int32)
	if err != nil {
		// Don't fail if custom columns can't be retrieved
		customColumns = []db.MatchCustomColumn{}
	}

	// Get language from cookie
	lang := cookies.GetLanguageFromCookie(c)

	writer := c.Response().Writer
	html := public.PublicSeasonSchedulePage(lang, season, matches, customColumns)
	c.Response().Status = http.StatusOK
	html.Render(writer)
	return nil
}

// GetPublicScheduleIndex shows a list of all public schedules
func (h *PublicHandler) GetPublicScheduleIndex(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)

	writer := c.Response().Writer
	html := public.PublicScheduleIndexPage(lang)
	c.Response().Status = http.StatusOK
	html.Render(writer)
	return nil
}

// GetPublicSeasonScoreboard serves the public scoreboard for a specific season
func (h *PublicHandler) GetPublicSeasonScoreboard(c echo.Context) error {
	seasonIDStr := c.Param("seasonId")
	seasonID, err := strconv.ParseInt(seasonIDStr, 10, 32)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid season ID")
	}

	seasonID32 := int32(seasonID)

	// Get season without requiring user authentication
	season, err := h.Queries.GetSeasonPublic(c.Request().Context(), seasonID32)
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "Season not found")
	}

	// Get scoreboard data for the season
	scoreboardParams := db.GetSeasonScoreboardParams{
		SeasonID: pgtype.Int4{Int32: seasonID32, Valid: true},
		ID:       seasonID32,
	}
	scoreboardData, err := h.Queries.GetSeasonScoreboard(c.Request().Context(), scoreboardParams)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get scoreboard")
	}

	// Get language from cookie
	lang := cookies.GetLanguageFromCookie(c)

	writer := c.Response().Writer
	html := public.PublicSeasonScoreboardPage(lang, season, scoreboardData)
	c.Response().Status = http.StatusOK
	html.Render(writer)
	return nil
}

// GetPublicScoreboardIndex shows a list of all public scoreboards
func (h *PublicHandler) GetPublicScoreboardIndex(c echo.Context) error {
	lang := cookies.GetLanguageFromCookie(c)

	writer := c.Response().Writer
	html := public.PublicScoreboardIndexPage(lang)
	c.Response().Status = http.StatusOK
	html.Render(writer)
	return nil
}
