package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/notifications"
	"github.com/j-em/coachpad/sse"
	templatesapphome "github.com/j-em/coachpad/templates/app/home"
	templatesapplogout "github.com/j-em/coachpad/templates/app/logout"
	"github.com/j-em/coachpad/utils/datamapper"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
)

type AppHandler struct {
	Queries             *db.Queries
	SSEManager          *sse.Manager
	NotificationService *notifications.Service
}

func (h *AppHandler) RegisterRoutes(r *echo.Group) {
	r.GET("/home", h.GetHome)
	r.POST("/logout", h.<PERSON><PERSON>and<PERSON>)
	r.POST("/test-sse", h.Test<PERSON>)
	r.GET("/sse", <PERSON><PERSON>)
}

// <PERSON><PERSON>ut<PERSON><PERSON>ler clears the session cookie and renders the logout success page
func (h *AppHandler) LogoutHandler(c echo.Context) error {
	// Expire the session cookie
	expiredCookie := &http.Cookie{
		Name:     "token",
		Value:    "",
		Path:     "/",
		Expires:  time.Unix(0, 0),
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteStrictMode,
	}
	c.SetCookie(expiredCookie)

	lang := cookies.GetLanguageFromCookie(c)
	node := templatesapplogout.LogoutSuccess(lang)
	return node.Render(c.Response().Writer)
}

func (h *AppHandler) GetHome(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	// Get the user object to check sidebar state
	user, err := h.Queries.GetUserByID(context.Background(), userID)
	if err != nil {
		fmt.Printf("Error fetching user: %v\n", err)
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to fetch user")
	}

	lang := cookies.GetLanguageFromCookie(c)

	// Get unread notification count
	unreadCount, err := h.NotificationService.GetUnreadCount(context.Background(), userID)
	if err != nil {
		unreadCount = 0 // Fallback to 0 if error
	}

	// Get user's active seasons
	seasonsWithPermissions, err := h.Queries.GetSeasonsWithPermissions(context.Background(), userID)
	if err != nil {
		fmt.Printf("Error fetching seasons: %v\n", err)
		node := templatesapphome.HomePage(templatesapphome.HomePageProps{
			Lang:          lang,
			ActiveLink:    "/app/home",
			Matches:       nil,
			Players:       nil,
			Seasons:       nil,
			IsSidebarOpen: user.IsSidebarOpen,
			UnreadCount:   unreadCount,
		})
		return node.Render(c.Response().Writer)
	}

	// Use datamapper for clean conversion - no manual loop needed!
	seasons := datamapper.ConvertSeasonsWithPermissions(seasonsWithPermissions)

	// Collect upcoming matches from all seasons
	var upcomingMatches []db.Match
	for _, season := range seasons {
		// Get upcoming matches for this season
		matches, err := h.Queries.GetSeasonUpcomingMatches(context.Background(), pgtype.Int4{
			Int32: season.ID,
			Valid: true,
		})

		if err != nil {
			continue // Skip this season if there's an error
		}

		upcomingMatches = append(upcomingMatches, matches...)
	}

	// get players
	players, err := h.Queries.GetPlayers(context.Background(), userID)
	if err != nil {

		fmt.Printf("Error fetching players: %v\n", err)
		players = nil
	}

	// Check if request is from htmx boosted navigation
	if c.Request().Header.Get("HX-Boosted") == "true" {
		node := templatesapphome.HomePageContent(lang, templatesapphome.HomePageContentProps{
			Lang:    lang,
			UserID:  userID,
			Players: players,
			Seasons: seasons,
			Matches: upcomingMatches,
		})
		return node.Render(c.Response().Writer)
	}
	node := templatesapphome.HomePage(templatesapphome.HomePageProps{
		Lang:          lang,
		UserID:        userID,
		ActiveLink:    "/app/home",
		Matches:       upcomingMatches,
		Players:       players,
		Seasons:       seasons,
		IsSidebarOpen: user.IsSidebarOpen,
		UnreadCount:   unreadCount,
	})
	return node.Render(c.Response().Writer)
}

// TestSSE sends a test SSE event to the current user
func (h *AppHandler) TestSSE(c echo.Context) error {
	userID := middleware.GetUserID(c)

	// Send test event to user
	testEvent := sse.Event{
		Event: "test-message",
		Data: map[string]any{
			"message":   "Hello from SSE!",
			"timestamp": time.Now().Format(time.RFC3339),
			"userID":    userID,
		},
		ID: fmt.Sprintf("test_%d", time.Now().UnixNano()),
	}

	h.SSEManager.SendToUser(userID, testEvent)

	return c.NoContent(http.StatusOK)
}

// HandleSSE handles Server-Sent Events connections for the home page
func (h *AppHandler) HandleSSE(c echo.Context) error {
	userID := middleware.GetUserID(c)
	clientID := fmt.Sprintf("home_user_%d_%d", userID, time.Now().UnixNano())

	// Set the stream parameter that the SSE library expects
	streamName := fmt.Sprintf("user_%d", userID)
	req := c.Request()
	q := req.URL.Query()
	q.Set("stream", streamName)
	req.URL.RawQuery = q.Encode()

	return h.SSEManager.HandleConnection(c, clientID, userID)
}
