package handlers

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/emailreminders"
	"github.com/j-em/coachpad/matchmaker"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/pkg/limits"
	templatesappseasons "github.com/j-em/coachpad/templates/app/seasons"
	"github.com/j-em/coachpad/templates/ui/toast"
	"github.com/j-em/coachpad/utils"
	"github.com/j-em/coachpad/utils/datamapper"
	"github.com/j-em/coachpad/utils/pagination"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
)

type SeasonsHandler struct {
	Queries              *db.Queries
	EmailService         *emailreminders.EmailReminderService
	PermissionMiddleware *middleware.PermissionMiddleware
	LimitsMiddleware     *middleware.LimitsMiddleware
	LimitsService        *limits.Service
}

type NewSeasonForm struct {
	Name           string  `form:"name" validate:"required"`
	Seasontype     string  `form:"seasontype" validate:"required,oneof=pool bowling other"`
	Frequency      string  `form:"frequency" validate:"required,oneof=weekly biweekly monthly quarterly yearly"`
	StartDate      string  `form:"startDate" validate:"required"`
	PlayerIds      []int32 `form:"playerIds[]" validate:"required,min=2"`
	AmountOfTables int32   `form:"amountOfTables" validate:"required,min=1"`
}

// Add Echo's built-in validator to the handler
func NewSeasonsHandler(queries *db.Queries, emailService *emailreminders.EmailReminderService, permissionMiddleware *middleware.PermissionMiddleware, limitsMiddleware *middleware.LimitsMiddleware, limitsService *limits.Service) *SeasonsHandler {
	return &SeasonsHandler{
		Queries:              queries,
		EmailService:         emailService,
		PermissionMiddleware: permissionMiddleware,
		LimitsMiddleware:     limitsMiddleware,
		LimitsService:        limitsService,
	}
}

func (h *SeasonsHandler) RegisterRoutes(r *echo.Group) {
	r.GET("/seasons/:seasonId", h.GetSeasonDetails)
	r.GET("/seasons/:seasonId/matches-table", h.GetMatchesTable)
	r.GET("/seasons/:seasonId/export/csv", h.ExportSeasonMatchesCSV)
	r.GET("/seasons/:seasonId/public-link", h.GetPublicScheduleLink)
	r.GET("/seasons/:seasonId/delete-modal", h.GetDeleteModal)
	r.GET("/seasons/:seasonId/rename-modal", h.GetRenameModal)
	r.DELETE("/seasons/:seasonId", h.DeleteSeason)
	r.PUT("/seasons/:seasonId", h.UpdateSeasonName)
	r.GET("/seasons/new", h.GetNewSeasonForm)

	// Apply limits middleware to season creation (includes match creation via bulk logic)
	if h.LimitsMiddleware != nil {
		r.POST("/seasons", h.PostNewSeason, h.LimitsMiddleware.CheckBulkResourceLimit(limits.ResourceMatches, "amountOfTables"))
	} else {
		r.POST("/seasons", h.PostNewSeason)
	}
}

func (h *SeasonsHandler) GetSeasonDetails(c echo.Context) error {
	seasonId := c.Param("seasonId")
	if seasonId == "" {
		return templatesappseasons.SeasonsError("Season ID is required").Render(c.Response().Writer)
	}

	userID := c.Get(middleware.UserIDKey).(int32)
	// Get the full user object
	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		return templatesappseasons.SeasonsError("Failed to fetch user").Render(c.Response().Writer)
	}

	// Convert seasonId string to int32
	seasonIdInt, err := strconv.ParseInt(seasonId, 10, 32)
	if err != nil {
		return templatesappseasons.SeasonsError("Invalid season ID format").Render(c.Response().Writer)
	}

	// Create pgtype.Int4 for the seasonId
	seasonIdPg := pgtype.Int4{
		Int32: int32(seasonIdInt),
		Valid: true,
	}

	// Get the season details using permission-aware query
	season, err := h.Queries.GetSeasonWithPermissionCheck(c.Request().Context(), db.GetSeasonWithPermissionCheckParams{
		ID:     int32(seasonIdInt),
		UserID: userID,
	})
	if err != nil {
		// log err
		fmt.Println("Error fetching season details:", err)
		return templatesappseasons.SeasonsError("Failed to fetch season details").Render(c.Response().Writer)
	}

	// Parse pagination parameters from query string
	pageStr := c.QueryParam("page")
	perPageStr := c.QueryParam("per_page")
	sort := c.QueryParam("sort")
	dir := c.QueryParam("dir")
	search := c.QueryParam("search")
	filterToday := c.QueryParam("filter_today") == "true"
	isPrint := c.QueryParam("print") == "true"

	// Set defaults
	page := 1
	itemsPerPage := 10
	direction := "asc"

	// For print requests, show all matches
	if isPrint {
		itemsPerPage = 9999
		page = 1
	} else {
		if pageStr != "" {
			if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
				page = p
			}
		}
		if perPageStr != "" {
			if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 {
				itemsPerPage = pp
			}
		}
	}
	if dir != "" {
		direction = dir
	}

	// Get all matches for the season
	allMatches, err := h.Queries.GetMatchesBySeasonId(context.Background(), seasonIdPg)
	if err != nil {
		return templatesappseasons.SeasonsError("Failed to fetch season matches").Render(c.Response().Writer)
	}

	// Apply filtering
	filteredMatches := filterMatches(allMatches, search)

	// Apply today filter
	if filterToday {
		today := time.Now().UTC().Format("2006-01-02")
		var todayMatches []db.GetMatchesBySeasonIdRow
		for _, match := range filteredMatches {
			// IMPORTANT: Only include matches with VALID dates that match today
			// Explicitly exclude matches with invalid/null dates
			if match.MatchDate.Valid {
				matchDateStr := match.MatchDate.Time.UTC().Format("2006-01-02")
				if matchDateStr == today {
					todayMatches = append(todayMatches, match)
				}
			}
		}
		filteredMatches = todayMatches
	}

	// Apply sorting
	sortedMatches := sortMatches(filteredMatches, sort, direction)
	totalItems := len(sortedMatches)
	totalPages := pagination.CalculateTotalPages(totalItems, itemsPerPage)

	// Apply pagination
	paginatedMatches := paginateMatches(sortedMatches, page, itemsPerPage)

	// Get custom match columns for the current user
	customColumns, err := h.Queries.GetMatchCustomColumns(context.Background(), userID)
	if err != nil {
		return templatesappseasons.SeasonsError("Failed to fetch custom match columns").Render(c.Response().Writer)
	}

	// Get custom values for paginated matches only
	customValues := make(map[int32][]db.MatchCustomValue)
	for _, match := range paginatedMatches {
		rows, err := h.Queries.GetMatchCustomValues(context.Background(), match.ID)
		if err != nil {
			continue // Skip if there's an error, log if needed
		}
		var values []db.MatchCustomValue
		for _, row := range rows {
			values = append(values, db.MatchCustomValue{
				ID:        row.ID,
				MatchID:   row.MatchID,
				ColumnID:  row.ColumnID,
				Value:     row.Value,
				CreatedAt: row.CreatedAt,
				UpdatedAt: row.UpdatedAt,
			})
		}
		customValues[match.ID] = values
	}

	// Get the seasons for the user
	seasonsWithPermissions, err := h.Queries.GetSeasonsWithPermissions(c.Request().Context(), userID)
	if err != nil {
		return templatesappseasons.SeasonsError("Failed to fetch seasons").Render(c.Response().Writer)
	}

	// Use datamapper for clean conversion - no manual loop needed!
	seasons := datamapper.ConvertSeasonsWithPermissions(seasonsWithPermissions)

	// Get language from cookie
	lang := cookies.GetLanguageFromCookie(c)

	// Create table params
	tableParams := templatesappseasons.MatchesTableParams{
		SortablePaginationParams: pagination.SortablePaginationParams{
			BasePaginationParams: pagination.BasePaginationParams{
				Page:         page,
				ItemsPerPage: itemsPerPage,
				Search:       search,
			},
			Sort:      sort,
			Direction: direction,
		},
		FilterToday: filterToday,
	}

	// Check if this is a print request
	if isPrint {
		return templatesappseasons.PrintMatches(templatesappseasons.PrintMatchesProps{
			Matches:    paginatedMatches,
			SeasonName: season.Name,
			Lang:       lang,
		}).Render(c.Response().Writer)
	}

	// Check if this is a boosted request
	if c.Request().Header.Get("HX-Boosted") == "true" {
		// Return just the page content for boost navigation
		content := templatesappseasons.SeasonDetailsPageContent(templatesappseasons.SeasonDetailsPageContentProps{
			Matches:       paginatedMatches,
			Lang:          lang,
			SeasonName:    season.Name,
			SeasonId:      season.ID,
			CustomColumns: customColumns,
			CustomValues:  customValues,
			Params:        tableParams,
			TotalItems:    totalItems,
			TotalPages:    totalPages,
		})
		return content.Render(c.Response().Writer)
	}

	// For regular requests, render the full page
	return templatesappseasons.SeasonsDetails(templatesappseasons.SeasonsDetailsProps{
		Matches:       paginatedMatches,
		Lang:          lang,
		SeasonName:    season.Name,
		IsSideBarOpen: user.IsSidebarOpen,
		Seasons:       seasons,
		SeasonId:      season.ID,
		CustomColumns: customColumns,
		CustomValues:  customValues,
		Params:        tableParams,
		TotalItems:    totalItems,
		TotalPages:    totalPages,
	}).Render(c.Response().Writer)
}

func (h *SeasonsHandler) GetMatchesTable(c echo.Context) error {
	seasonId := c.Param("seasonId")
	if seasonId == "" {
		return templatesappseasons.SeasonsError("Season ID is required").Render(c.Response().Writer)
	}

	userID := c.Get(middleware.UserIDKey).(int32)

	// Convert seasonId string to int32
	seasonIdInt, err := strconv.ParseInt(seasonId, 10, 32)
	if err != nil {
		return templatesappseasons.SeasonsError("Invalid season ID format").Render(c.Response().Writer)
	}

	// Create pgtype.Int4 for the seasonId
	seasonIdPg := pgtype.Int4{
		Int32: int32(seasonIdInt),
		Valid: true,
	}

	// Get the season details using permission-aware query
	season, err := h.Queries.GetSeasonWithPermissionCheck(c.Request().Context(), db.GetSeasonWithPermissionCheckParams{
		ID:     int32(seasonIdInt),
		UserID: userID,
	})
	if err != nil {
		fmt.Println("Error fetching season details:", err)
		return templatesappseasons.SeasonsError("Failed to fetch season details").Render(c.Response().Writer)
	}

	// Parse pagination parameters from query string
	pageStr := c.QueryParam("page")
	perPageStr := c.QueryParam("per_page")
	sort := c.QueryParam("sort")
	dir := c.QueryParam("dir")
	search := c.QueryParam("search")
	filterToday := c.QueryParam("filter_today") == "true"

	// Set defaults
	page := 1
	itemsPerPage := 10
	direction := "asc"

	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}
	if perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 {
			itemsPerPage = pp
		}
	}
	if dir != "" {
		direction = dir
	}

	// Get all matches for the season
	allMatches, err := h.Queries.GetMatchesBySeasonId(context.Background(), seasonIdPg)
	if err != nil {
		return templatesappseasons.SeasonsError("Failed to fetch season matches").Render(c.Response().Writer)
	}

	// Apply filtering
	filteredMatches := filterMatches(allMatches, search)

	// Apply today filter
	if filterToday {
		today := time.Now().UTC().Format("2006-01-02")
		var todayMatches []db.GetMatchesBySeasonIdRow
		for _, match := range filteredMatches {
			// IMPORTANT: Only include matches with VALID dates that match today
			// Explicitly exclude matches with invalid/null dates
			if match.MatchDate.Valid {
				matchDateStr := match.MatchDate.Time.UTC().Format("2006-01-02")
				if matchDateStr == today {
					todayMatches = append(todayMatches, match)
				}
			}
		}
		filteredMatches = todayMatches
	}

	// Apply sorting
	sortedMatches := sortMatches(filteredMatches, sort, direction)
	totalItems := len(sortedMatches)
	totalPages := pagination.CalculateTotalPages(totalItems, itemsPerPage)

	// Apply pagination
	paginatedMatches := paginateMatches(sortedMatches, page, itemsPerPage)

	// Get custom match columns for the current user
	customColumns, err := h.Queries.GetMatchCustomColumns(context.Background(), userID)
	if err != nil {
		return templatesappseasons.SeasonsError("Failed to fetch custom match columns").Render(c.Response().Writer)
	}

	// Get custom values for paginated matches only
	customValues := make(map[int32][]db.MatchCustomValue)
	for _, match := range paginatedMatches {
		rows, err := h.Queries.GetMatchCustomValues(context.Background(), match.ID)
		if err != nil {
			continue // Skip if there's an error, log if needed
		}
		var values []db.MatchCustomValue
		for _, row := range rows {
			values = append(values, db.MatchCustomValue{
				ID:        row.ID,
				MatchID:   row.MatchID,
				ColumnID:  row.ColumnID,
				Value:     row.Value,
				CreatedAt: row.CreatedAt,
				UpdatedAt: row.UpdatedAt,
			})
		}
		customValues[match.ID] = values
	}

	// Get language from cookie
	lang := cookies.GetLanguageFromCookie(c)

	// Create table params
	tableParams := templatesappseasons.MatchesTableParams{
		SortablePaginationParams: pagination.SortablePaginationParams{
			BasePaginationParams: pagination.BasePaginationParams{
				Page:         page,
				ItemsPerPage: itemsPerPage,
				Search:       search,
			},
			Sort:      sort,
			Direction: direction,
		},
		FilterToday: filterToday,
	}

	// Render only the matches table content (for HTMX updates)
	return templatesappseasons.MatchesTableContent(templatesappseasons.MatchesTableProps{
		Matches:       paginatedMatches,
		Lang:          lang,
		SeasonName:    season.Name,
		SeasonId:      season.ID,
		CustomColumns: customColumns,
		CustomValues:  customValues,
		Params:        tableParams,
		TotalItems:    totalItems,
		TotalPages:    totalPages,
	}).Render(c.Response().Writer)
}

func (h *SeasonsHandler) GetNewSeasonForm(c echo.Context) error {
	// Get user ID from context (set by your auth middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Get the language from the context
	lang := cookies.GetLanguageFromCookie(c)

	// Get the full user object
	user, err := h.Queries.GetUserByID(c.Request().Context(), userID)
	if err != nil {
		return templatesappseasons.SeasonsError("Failed to fetch user").Render(c.Response().Writer)
	}

	// Get all players for the user
	allPlayers, err := h.Queries.GetPlayers(c.Request().Context(), userID)
	if err != nil {
		return templatesappseasons.SeasonsError("Failed to fetch players").Render(c.Response().Writer)
	}

	// Get the seasons for the user
	seasonsWithPermissions, err := h.Queries.GetSeasonsWithPermissions(c.Request().Context(), userID)
	if err != nil {
		return templatesappseasons.SeasonsError("Failed to fetch seasons").Render(c.Response().Writer)
	}

	// Use datamapper for clean conversion - no manual loop needed!
	seasons := datamapper.ConvertSeasonsWithPermissions(seasonsWithPermissions)

	// If htmx boosted request, render only the page content
	if c.Request().Header.Get("HX-Boosted") == "true" {
		// Render only the content for htmx requests
		return templatesappseasons.SeasonsNewPageContent(templatesappseasons.SeasonsNewPageContentProps{
			Seasons:       seasons,
			AllPlayers:    allPlayers,
			IsSidebarOpen: user.IsSidebarOpen,
			ActiveLink:    "/app/seasons/new",
			Lang:          lang,
		}).Render(c.Response().Writer)
	}

	return templatesappseasons.SeasonsNewPage(templatesappseasons.SeasonsNewPageProps{
		IsSidebarOpen: user.IsSidebarOpen,
		Seasons:       seasons,
		AllPlayers:    allPlayers,
		ActiveLink:    "/app/seasons/new",
		Lang:          lang,
	}).Render(c.Response().Writer)
}

func (h *SeasonsHandler) PostNewSeason(c echo.Context) error {
	// Get user ID from context (set by your auth middleware)
	userID := c.Get(middleware.UserIDKey).(int32)

	// Get the language from the context
	lang := cookies.GetLanguageFromCookie(c)

	// Create a new form instance
	var form NewSeasonForm

	// Bind and validate the form values using Echo's built-in validator
	if err := c.Bind(&form); err != nil {
		return templatesappseasons.SeasonsError("Invalid form data").Render(c.Response().Writer)
	}
	if err := c.Validate(&form); err != nil {
		return templatesappseasons.SeasonsError("Form validation failed: " + err.Error()).Render(c.Response().Writer)
	}

	// Parse the start date
	startDate, err := time.Parse("2006-01-02", form.StartDate)
	if err != nil {
		return templatesappseasons.SeasonsError("Invalid date format").Render(c.Response().Writer)
	}

	// Create the season in the database
	season, err := h.Queries.CreateSeason(c.Request().Context(), db.CreateSeasonParams{
		UserID: pgtype.Int4{Int32: userID, Valid: true},
		Name:   form.Name,
		StartDate: pgtype.Date{
			Time:  startDate,
			Valid: true,
		},
		SeasonType: form.Seasontype,
		Frequency:  form.Frequency,
	})

	if err != nil {
		fmt.Println(err)
		return templatesappseasons.SeasonsError("Failed to create season").Render(c.Response().Writer)
	}

	// Generate matches using the round-robin matchmaker
	config := matchmaker.RoundRobinConfig{
		PlayerIDs:      form.PlayerIds,
		StartDate:      startDate,
		Frequency:      form.Frequency,
		AmountOfTables: form.AmountOfTables,
	}

	matches := matchmaker.GenerateRoundRobin(config)

	// Create matches in the database and collect them for email reminder scheduling
	var createdMatches []db.Match
	for _, match := range matches {
		createMatchParams := db.CreateMatchParams{
			SeasonID: pgtype.Int4{
				Int32: season.ID,
				Valid: true,
			},
			PlayerId1: pgtype.Int4{
				Int32: match.Player1ID,
				Valid: true,
			},
			PlayerId2: pgtype.Int4{
				Int32: match.Player2ID,
				Valid: true,
			},
			PlayerId1Points: pgtype.Int4{},
			PlayerId2Points: pgtype.Int4{},
			MatchDate: pgtype.Date{
				Time:  match.MatchDate,
				Valid: true,
			},
		}

		createdMatch, err := h.Queries.CreateMatch(c.Request().Context(), createMatchParams)
		if err != nil {
			// Log the error but continue creating other matches
			c.Logger().Errorf("Failed to create match: %v", err)
		} else {
			createdMatches = append(createdMatches, createdMatch)
		}
	}

	// Schedule email reminders for all created matches
	if h.EmailService.IsEnabled() && len(createdMatches) > 0 {
		go func() {
			// Get players with notifications enabled for this season
			players, err := h.Queries.GetPlayersWithNotificationsEnabled(context.Background(), season.ID)
			if err != nil {
				return
			}

			// Convert to db.Player slice
			var dbPlayers []db.Player
			for _, p := range players {
				player := db.Player{
					ID:                        p.ID,
					Name:                      p.Name,
					Email:                     p.Email,
					EmailNotificationsEnabled: true,
					EmailReminderPreferences:  p.EmailReminderPreferences,
				}
				dbPlayers = append(dbPlayers, player)
			}

			// Schedule reminders for each match
			for _, match := range createdMatches {
				if err := h.EmailService.ScheduleMatchReminders(context.Background(), match, dbPlayers); err != nil {
					// Log error but don't fail the request
				}
			}
		}()
	}

	// Render just the success message component
	// Pass the season ID and language to the success message component
	return templatesappseasons.SeasonsPostSuccess(fmt.Sprintf("%d", season.ID), lang).Render(c.Response().Writer)
}

func (h *SeasonsHandler) ExportSeasonMatchesCSV(c echo.Context) error {
	seasonId := c.Param("seasonId")
	if seasonId == "" {
		return c.JSON(400, map[string]string{"error": "Season ID is required"})
	}

	userID := c.Get(middleware.UserIDKey).(int32)

	// Convert seasonId string to int32
	seasonIdInt, err := strconv.ParseInt(seasonId, 10, 32)
	if err != nil {
		return c.JSON(400, map[string]string{"error": "Invalid season ID format"})
	}

	// Verify user owns this season
	season, err := h.Queries.GetSeason(c.Request().Context(), db.GetSeasonParams{
		ID:     int32(seasonIdInt),
		UserID: pgtype.Int4{Int32: userID, Valid: true},
	})
	if err != nil {
		return c.JSON(404, map[string]string{"error": "Season not found"})
	}

	// Create pgtype.Int4 for the seasonId
	seasonIdPg := pgtype.Int4{
		Int32: int32(seasonIdInt),
		Valid: true,
	}

	// Get all matches for the season
	matches, err := h.Queries.GetMatchesBySeasonId(c.Request().Context(), seasonIdPg)
	if err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to fetch season matches"})
	}

	// Get custom match columns for the current user
	customColumns, err := h.Queries.GetMatchCustomColumns(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to fetch custom match columns"})
	}

	// Get custom values for matches
	customValues := make(map[int32][]db.MatchCustomValue)
	for _, match := range matches {
		rows, err := h.Queries.GetMatchCustomValues(c.Request().Context(), match.ID)
		if err != nil {
			continue // Skip if there's an error
		}
		var values []db.MatchCustomValue
		for _, row := range rows {
			values = append(values, db.MatchCustomValue{
				ID:        row.ID,
				MatchID:   row.MatchID,
				ColumnID:  row.ColumnID,
				Value:     row.Value,
				CreatedAt: row.CreatedAt,
				UpdatedAt: row.UpdatedAt,
			})
		}
		customValues[match.ID] = values
	}

	// Set up CSV response using shared utilities
	config := utils.NewCSVExportConfig(fmt.Sprintf("%s_matches.csv", season.Name))
	writer := utils.SetupCSVResponse(c, config)
	defer writer.Flush()

	// Write CSV header
	header := []string{
		"ID",
		"Match Date",
		"Player 1",
		"Player 1 Points",
		"Player 2",
		"Player 2 Points",
		"Winner",
		"Match Group",
		"Active",
		"Created At",
		"Updated At",
	}

	// Add custom column headers for active columns
	for _, column := range customColumns {
		if column.IsActive.Bool {
			header = append(header, column.Name)
		}
	}

	if err := writer.Write(header); err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to write CSV header"})
	}

	// Write match data
	for _, match := range matches {
		record := []string{
			fmt.Sprintf("%d", match.ID),
			utils.FormatCSVDate(match.MatchDate),
			match.Player1Name.String,
			utils.FormatCSVInt32(match.PlayerId1Points),
			match.Player2Name.String,
			utils.FormatCSVInt32(match.PlayerId2Points),
			formatWinner(match.WinnerID),
			fmt.Sprintf("%d", match.MatchGroup),
			utils.FormatCSVBool(match.IsActive),
			utils.FormatCSVTimestamp(match.CreatedAt),
			utils.FormatCSVTimestamp(match.UpdatedAt),
		}

		// Add custom column values
		for _, column := range customColumns {
			if column.IsActive.Bool {
				value := ""
				if matchValues, exists := customValues[match.ID]; exists {
					for _, customValue := range matchValues {
						if customValue.ColumnID == column.ID {
							if customValue.Value.Valid {
								value = customValue.Value.String
							}
							break
						}
					}
				}
				record = append(record, value)
			}
		}

		if err := writer.Write(record); err != nil {
			return c.JSON(500, map[string]string{"error": "Failed to write CSV data"})
		}
	}

	return nil
}

func formatWinner(winnerID pgtype.Int4) string {
	if !winnerID.Valid {
		return ""
	}
	return fmt.Sprintf("Player ID: %d", winnerID.Int32)
}

func (h *SeasonsHandler) GetPublicScheduleLink(c echo.Context) error {
	seasonId := c.Param("seasonId")
	if seasonId == "" {
		return c.JSON(400, map[string]string{"error": "Season ID is required"})
	}

	userID := c.Get(middleware.UserIDKey).(int32)

	// Convert seasonId string to int32
	seasonIdInt, err := strconv.ParseInt(seasonId, 10, 32)
	if err != nil {
		return c.JSON(400, map[string]string{"error": "Invalid season ID format"})
	}

	// Verify user owns this season
	_, err = h.Queries.GetSeason(c.Request().Context(), db.GetSeasonParams{
		ID:     int32(seasonIdInt),
		UserID: pgtype.Int4{Int32: userID, Valid: true},
	})
	if err != nil {
		return c.JSON(404, map[string]string{"error": "Season not found"})
	}

	// Generate the public schedule link
	baseURL := c.Scheme() + "://" + c.Request().Host
	publicLink := fmt.Sprintf("%s/public/seasons/%s", baseURL, seasonId)

	// Return a toast notification with the link
	toastComponent := toast.Toast(toast.ToastConfig{
		ID:         "public-link-copied-toast",
		Message:    "Public schedule link copied to clipboard!",
		Style:      "success",
		DataTestID: "public-link-copied-toast",
		AutoClose:  true,
	})

	// Set response headers for HTMX
	utils.SetTypedTrigger(c, utils.HXEventCopyToClipboard, utils.NewCopyToClipboardEvent(publicLink))

	return toastComponent.Render(c.Response().Writer)
}

type UpdateSeasonNameForm struct {
	Name string `form:"name" validate:"required"`
}

func (h *SeasonsHandler) GetDeleteModal(c echo.Context) error {
	seasonId := c.Param("seasonId")
	if seasonId == "" {
		return c.JSON(400, map[string]string{"error": "Season ID is required"})
	}

	userID := c.Get(middleware.UserIDKey).(int32)

	// Convert seasonId string to int32
	seasonIdInt, err := strconv.ParseInt(seasonId, 10, 32)
	if err != nil {
		return c.JSON(400, map[string]string{"error": "Invalid season ID format"})
	}

	// Verify user owns this season
	season, err := h.Queries.GetSeason(c.Request().Context(), db.GetSeasonParams{
		ID:     int32(seasonIdInt),
		UserID: pgtype.Int4{Int32: userID, Valid: true},
	})
	if err != nil {
		return c.JSON(404, map[string]string{"error": "Season not found"})
	}

	// Get language from cookie
	lang := cookies.GetLanguageFromCookie(c)

	return templatesappseasons.DeleteConfirmModal(templatesappseasons.DeleteConfirmModalProps{
		SeasonID:   season.ID,
		SeasonName: season.Name,
		Lang:       lang,
	}).Render(c.Response().Writer)
}

func (h *SeasonsHandler) GetRenameModal(c echo.Context) error {
	seasonId := c.Param("seasonId")
	if seasonId == "" {
		return c.JSON(400, map[string]string{"error": "Season ID is required"})
	}

	userID := c.Get(middleware.UserIDKey).(int32)

	// Convert seasonId string to int32
	seasonIdInt, err := strconv.ParseInt(seasonId, 10, 32)
	if err != nil {
		return c.JSON(400, map[string]string{"error": "Invalid season ID format"})
	}

	// Verify user owns this season
	season, err := h.Queries.GetSeason(c.Request().Context(), db.GetSeasonParams{
		ID:     int32(seasonIdInt),
		UserID: pgtype.Int4{Int32: userID, Valid: true},
	})
	if err != nil {
		return c.JSON(404, map[string]string{"error": "Season not found"})
	}

	// Get language from cookie
	lang := cookies.GetLanguageFromCookie(c)

	return templatesappseasons.RenameModal(templatesappseasons.RenameModalProps{
		SeasonID:   season.ID,
		SeasonName: season.Name,
		Lang:       lang,
	}).Render(c.Response().Writer)
}

func (h *SeasonsHandler) DeleteSeason(c echo.Context) error {
	seasonId := c.Param("seasonId")
	if seasonId == "" {
		return c.JSON(400, map[string]string{"error": "Season ID is required"})
	}

	userID := c.Get(middleware.UserIDKey).(int32)

	// Convert seasonId string to int32
	seasonIdInt, err := strconv.ParseInt(seasonId, 10, 32)
	if err != nil {
		return c.JSON(400, map[string]string{"error": "Invalid season ID format"})
	}

	// Verify user owns this season before deleting and get current data
	season, err := h.Queries.GetSeason(c.Request().Context(), db.GetSeasonParams{
		ID:     int32(seasonIdInt),
		UserID: pgtype.Int4{Int32: userID, Valid: true},
	})
	if err != nil {
		return c.JSON(404, map[string]string{"error": "Season not found"})
	}

	// Soft delete the season by setting is_active to false
	_, err = h.Queries.UpdateSeason(c.Request().Context(), db.UpdateSeasonParams{
		Name:       season.Name,
		StartDate:  season.StartDate,
		SeasonType: season.SeasonType,
		Frequency:  season.Frequency,
		IsActive:   false,
		ID:         int32(seasonIdInt),
		UserID:     pgtype.Int4{Int32: userID, Valid: true},
	})
	if err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to delete season"})
	}

	// Get language from cookie
	lang := cookies.GetLanguageFromCookie(c)

	// Close modal and trigger update season event
	utils.SetMultipleTypedTriggers(c, []utils.TypedEventTrigger{
		{EventName: utils.HXEventCloseAllModals, Data: utils.CloseAllModalsEvent{}},
		{EventName: utils.HXEventUpdateSeason, Data: utils.NewUpdateSeasonEvent(int64(seasonIdInt))},
	})
	return templatesappseasons.DeleteSuccess(lang).Render(c.Response().Writer)
}

func (h *SeasonsHandler) UpdateSeasonName(c echo.Context) error {
	seasonId := c.Param("seasonId")
	if seasonId == "" {
		return c.JSON(400, map[string]string{"error": "Season ID is required"})
	}

	userID := c.Get(middleware.UserIDKey).(int32)

	// Convert seasonId string to int32
	seasonIdInt, err := strconv.ParseInt(seasonId, 10, 32)
	if err != nil {
		return c.JSON(400, map[string]string{"error": "Invalid season ID format"})
	}

	// Bind and validate form
	var form UpdateSeasonNameForm
	if err := c.Bind(&form); err != nil {
		return c.JSON(400, map[string]string{"error": "Invalid form data"})
	}
	if err := c.Validate(&form); err != nil {
		return c.JSON(400, map[string]string{"error": "Form validation failed: " + err.Error()})
	}

	// Verify user owns this season before updating
	season, err := h.Queries.GetSeason(c.Request().Context(), db.GetSeasonParams{
		ID:     int32(seasonIdInt),
		UserID: pgtype.Int4{Int32: userID, Valid: true},
	})
	if err != nil {
		return c.JSON(404, map[string]string{"error": "Season not found"})
	}

	// Update the season name
	_, err = h.Queries.UpdateSeason(c.Request().Context(), db.UpdateSeasonParams{
		ID:         int32(seasonIdInt),
		UserID:     pgtype.Int4{Int32: userID, Valid: true},
		Name:       form.Name,
		StartDate:  season.StartDate,
		SeasonType: season.SeasonType,
		Frequency:  season.Frequency,
		IsActive:   season.IsActive,
	})
	if err != nil {
		return c.JSON(500, map[string]string{"error": "Failed to update season name"})
	}

	// Get language from cookie
	lang := cookies.GetLanguageFromCookie(c)

	// Return success toast and trigger navigation refresh
	utils.SetMultipleTypedTriggers(c, []utils.TypedEventTrigger{
		{EventName: utils.HXEventCloseAllModals, Data: utils.CloseAllModalsEvent{}},
		{EventName: utils.HXEventUpdateSeason, Data: utils.NewUpdateSeasonEvent(int64(seasonIdInt))},
	})
	return templatesappseasons.RenameSuccess(lang).Render(c.Response().Writer)
}

// sortMatches sorts matches based on field and direction
func sortMatches(matches []db.GetMatchesBySeasonIdRow, sortField, direction string) []db.GetMatchesBySeasonIdRow {
	if len(matches) == 0 {
		return matches
	}

	sorted := make([]db.GetMatchesBySeasonIdRow, len(matches))
	copy(sorted, matches)

	sort.Slice(sorted, func(i, j int) bool {
		var aVal, bVal string
		switch sortField {
		case "matchDate":
			aVal, bVal = sorted[i].MatchDate.Time.Format("2006-01-02"), sorted[j].MatchDate.Time.Format("2006-01-02")
		case "player_id1":
			aVal, bVal = sorted[i].Player1Name.String, sorted[j].Player1Name.String
		case "playerId1Points":
			aVal, bVal = strconv.Itoa(int(sorted[i].PlayerId1Points.Int32)), strconv.Itoa(int(sorted[j].PlayerId1Points.Int32))
		case "player_id2":
			aVal, bVal = sorted[i].Player2Name.String, sorted[j].Player2Name.String
		case "playerId2Points":
			aVal, bVal = strconv.Itoa(int(sorted[i].PlayerId2Points.Int32)), strconv.Itoa(int(sorted[j].PlayerId2Points.Int32))
		case "matchGroup":
			aVal, bVal = strconv.Itoa(int(sorted[i].MatchGroup)), strconv.Itoa(int(sorted[j].MatchGroup))
		default:
			aVal, bVal = sorted[i].MatchDate.Time.Format("2006-01-02"), sorted[j].MatchDate.Time.Format("2006-01-02")
		}

		if direction == "desc" {
			return strings.Compare(aVal, bVal) > 0
		}
		return strings.Compare(aVal, bVal) < 0
	})

	return sorted
}

// filterMatches filters matches based on search term
func filterMatches(matches []db.GetMatchesBySeasonIdRow, search string) []db.GetMatchesBySeasonIdRow {
	if search == "" {
		return matches
	}

	searchLower := strings.ToLower(search)
	var filtered []db.GetMatchesBySeasonIdRow

	for _, match := range matches {
		// Search in player names, match group, and date
		matchText := strings.ToLower(fmt.Sprintf("%s %s %d %s",
			match.Player1Name.String,
			match.Player2Name.String,
			match.MatchGroup,
			match.MatchDate.Time.Format("2006-01-02")))
		if strings.Contains(matchText, searchLower) {
			filtered = append(filtered, match)
		}
	}

	return filtered
}

// paginateMatches applies pagination to matches slice
func paginateMatches(matches []db.GetMatchesBySeasonIdRow, page, itemsPerPage int) []db.GetMatchesBySeasonIdRow {
	if len(matches) == 0 {
		return matches
	}

	// Validate input parameters
	if page < 1 {
		page = 1
	}
	if itemsPerPage < 1 {
		itemsPerPage = 1
	}

	startIdx := (page - 1) * itemsPerPage
	endIdx := startIdx + itemsPerPage

	if startIdx >= len(matches) {
		return []db.GetMatchesBySeasonIdRow{}
	}

	if endIdx > len(matches) {
		endIdx = len(matches)
	}

	return matches[startIdx:endIdx]
}
