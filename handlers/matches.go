package handlers

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/emailreminders"
	"github.com/j-em/coachpad/middleware"
	"github.com/j-em/coachpad/pkg/limits"
	"github.com/j-em/coachpad/utils/hx"

	templatesappseasons "github.com/j-em/coachpad/templates/app/seasons"
	matchrow "github.com/j-em/coachpad/templates/ui/matchRow"
	"github.com/j-em/coachpad/utils/pagination"
	"github.com/j-em/coachpad/utils/typeutils"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
)

// MatchesHandler handles match-related routes
type MatchesHandler struct {
	Queries          *db.Queries
	EmailService     *emailreminders.EmailReminderService
	LimitsMiddleware *middleware.LimitsMiddleware
}

// RegisterRoutes registers match-related routes under the given group
func (h *MatchesHandler) RegisterRoutes(r *echo.Group) {
	r.GET("/matches/:matchId/player1/edit", h.GetEditPlayer1)
	r.GET("/matches/:matchId/player2/edit", h.GetEditPlayer2)

	// Apply limits middleware to match creation
	if h.LimitsMiddleware != nil {
		r.POST("/matches", h.CreateMatch, h.LimitsMiddleware.CheckResourceLimit(limits.ResourceMatches))
	} else {
		r.POST("/matches", h.CreateMatch)
	}

	r.PATCH("/matches/:matchId", h.PatchMatch)
	r.DELETE("/matches/:matchId", h.DeleteMatch)
	// r.PUT("/matches/:matchId/custom-values", h.UpdateCustomValue) // TODO: Implement when custom value queries are available
}

// parsePlayerSelectParams parses pagination and search parameters for player selection
func parsePlayerSelectParams(c echo.Context) templatesappseasons.PlayerSelectParams {
	return templatesappseasons.PlayerSelectParams{
		BasePaginationParams: pagination.ParseBasePagination(c),
	}
}

// GetEditPlayer1 handles GET /app/matches/:matchId/player1/edit
func (h *MatchesHandler) GetEditPlayer1(c echo.Context) error {
	// Get user ID from context
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	// Parse matchId from URL
	matchIdStr := c.Param("matchId")
	matchId, err := strconv.Atoi(matchIdStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid match ID")
	}

	// Parse pagination parameters
	params := parsePlayerSelectParams(c)

	// Fetch the match
	match, err := h.Queries.GetMatch(context.Background(), int32(matchId))
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "match not found")
	}

	// Fetch all players for the user
	players, err := h.Queries.GetPlayers(context.Background(), userID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to fetch players")
	}

	// Exclude current player1
	var availablePlayers []db.Player
	for _, p := range players {
		if match.PlayerId1.Valid && p.ID == match.PlayerId1.Int32 {
			continue
		}
		availablePlayers = append(availablePlayers, db.Player{
			ID:   p.ID,
			Name: p.Name,
		})
	}

	// Render the player selection UI
	lang := cookies.GetLanguageFromCookie(c)
	node := templatesappseasons.Player1SelectForMatch(match.ID, match.PlayerId1.Int32, availablePlayers, params, lang)
	return node.Render(c.Response().Writer)
}

// GetEditPlayer2 handles GET /app/matches/:matchId/player2/edit
func (h *MatchesHandler) GetEditPlayer2(c echo.Context) error {
	// Get user ID from context
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	// Parse matchId from URL
	matchIdStr := c.Param("matchId")
	matchId, err := strconv.Atoi(matchIdStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid match ID")
	}

	// Parse pagination parameters
	params := parsePlayerSelectParams(c)

	// Fetch the match
	match, err := h.Queries.GetMatch(context.Background(), int32(matchId))
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "match not found")
	}

	// Fetch all players for the user
	players, err := h.Queries.GetPlayers(context.Background(), userID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to fetch players")
	}

	// Exclude current player2
	var availablePlayers []db.Player
	for _, p := range players {
		if match.PlayerId2.Valid && p.ID == match.PlayerId2.Int32 {
			continue
		}
		availablePlayers = append(availablePlayers, db.Player{
			ID:   p.ID,
			Name: p.Name,
		})
	}

	// Render the player selection UI
	lang := cookies.GetLanguageFromCookie(c)
	node := templatesappseasons.Player2SelectForMatch(match.ID, match.PlayerId2.Int32, availablePlayers, params, lang)
	return node.Render(c.Response().Writer)
}

// CreateMatch handles POST /app/matches
func (h *MatchesHandler) CreateMatch(c echo.Context) error {
	// Get user ID from context
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	// Parse JSON body
	var matchData struct {
		ID              int32  `json:"id"` // Frontend sends this but we use URL param
		SeasonId        int32  `json:"seasonId"`
		PlayerId1       *int32 `json:"playerId1"`
		PlayerId1Points any    `json:"playerId1Points"`
		PlayerId2       *int32 `json:"playerId2"`
		PlayerId2Points any    `json:"playerId2Points"`
		MatchDate       string `json:"matchDate"`
		WinnerId        *int32 `json:"winnerId"`
		MatchGroup      int32  `json:"matchGroup"`
		IsActive        *bool  `json:"isActive"`
	}

	if err := c.Bind(&matchData); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid JSON: "+err.Error())
	}

	if err := c.Validate(&matchData); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "validation failed: "+err.Error())
	}

	// Verify season ownership
	_, err := h.Queries.GetSeason(context.Background(), db.GetSeasonParams{
		ID:     matchData.SeasonId,
		UserID: pgtype.Int4{Int32: userID, Valid: true},
	})
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "season not found")
	}

	// Parse match date
	matchDate, err := time.Parse("2006-01-02", matchData.MatchDate)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid date format")
	}

	// Create match in database
	createParams := db.CreateMatchParams{
		SeasonID:        pgtype.Int4{Int32: matchData.SeasonId, Valid: true},
		PlayerId1:       pgtype.Int4{Int32: typeutils.GetInt32Value(matchData.PlayerId1), Valid: matchData.PlayerId1 != nil},
		PlayerId1Points: pgtype.Int4{Int32: typeutils.ConvertToInt32(matchData.PlayerId1Points), Valid: true},
		PlayerId2:       pgtype.Int4{Int32: typeutils.GetInt32Value(matchData.PlayerId2), Valid: matchData.PlayerId2 != nil},
		PlayerId2Points: pgtype.Int4{Int32: typeutils.ConvertToInt32(matchData.PlayerId2Points), Valid: true},
		MatchDate:       pgtype.Date{Time: matchDate, Valid: true},
		MatchGroup:      matchData.MatchGroup,
	}

	match, err := h.Queries.CreateMatch(context.Background(), createParams)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to create match: "+err.Error())
	}

	return c.JSON(http.StatusCreated, match)
}

// PatchMatch handles PATCH /app/matches/:matchId - unified handler for partial match updates
func (h *MatchesHandler) PatchMatch(c echo.Context) error {
	// Get user ID from context
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	// Parse matchId from URL
	matchIdStr := c.Param("matchId")
	matchId, err := strconv.Atoi(matchIdStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid match ID")
	}

	// Parse partial update data - any combination of fields can be provided
	var matchData struct {
		ID            *int32  `json:"id" form:"id"`
		SeasonId      *int32  `json:"seasonId" form:"seasonId"`
		PlayerId1     *int32  `json:"player_id1" form:"player_id1"`
		Player1Points *string `json:"player1Points" form:"player1Points"`
		Player2Points *string `json:"player2Points" form:"player2Points"`
		PlayerId2     *int32  `json:"player_id2" form:"player_id2"`
		MatchDate     *string `json:"matchDate" form:"matchDate"`
		WinnerId      *int32  `json:"winnerId" form:"winnerId"`
		MatchGroup    *int32  `json:"matchGroup" form:"matchGroup"`
		IsActive      *bool   `json:"isActive" form:"isActive"`
	}

	if err := c.Bind(&matchData); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid data: "+err.Error())
	}

	if err := c.Validate(&matchData); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "validation failed: "+err.Error())
	}

	// Get current match to verify ownership and get current values
	currentMatch, err := h.Queries.GetMatch(context.Background(), int32(matchId))
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "match not found")
	}

	// Verify user owns the season
	season, err := h.Queries.GetSeason(context.Background(), db.GetSeasonParams{
		ID:     currentMatch.SeasonID.Int32,
		UserID: pgtype.Int4{Int32: userID, Valid: true},
	})
	if err != nil {
		return echo.NewHTTPError(http.StatusForbidden, "access denied")
	}

	// Suppress unused variable warning
	_ = season

	// Validate player ownership if players are being updated
	if matchData.PlayerId1 != nil || matchData.PlayerId2 != nil {
		players, err := h.Queries.GetPlayers(context.Background(), userID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "failed to fetch players")
		}

		// Validate player1 ownership
		if matchData.PlayerId1 != nil {
			valid := false
			for _, p := range players {
				if p.ID == *matchData.PlayerId1 {
					valid = true
					break
				}
			}
			if !valid {
				return echo.NewHTTPError(http.StatusForbidden, "player1 not allowed")
			}
		}

		// Validate player2 ownership
		if matchData.PlayerId2 != nil {
			valid := false
			for _, p := range players {
				if p.ID == *matchData.PlayerId2 {
					valid = true
					break
				}
			}
			if !valid {
				return echo.NewHTTPError(http.StatusForbidden, "player2 not allowed")
			}
		}
	}

	// Build update params using current values as defaults and only updating provided fields
	// Season ID (use current if not provided)
	seasonId := currentMatch.SeasonID
	if matchData.SeasonId != nil {
		seasonId = pgtype.Int4{Int32: *matchData.SeasonId, Valid: true}
	}

	// Players (use current if not provided)
	playerId1 := currentMatch.PlayerId1
	if matchData.PlayerId1 != nil {
		playerId1 = pgtype.Int4{Int32: *matchData.PlayerId1, Valid: true}
	}
	playerId2 := currentMatch.PlayerId2
	if matchData.PlayerId2 != nil {
		playerId2 = pgtype.Int4{Int32: *matchData.PlayerId2, Valid: true}
	}

	// Points (use current if not provided)
	playerId1Points := currentMatch.PlayerId1Points
	if matchData.Player1Points != nil && *matchData.Player1Points != "" {
		playerId1Points = pgtype.Int4{Int32: typeutils.ConvertToInt32(*matchData.Player1Points), Valid: true}
	}
	playerId2Points := currentMatch.PlayerId2Points
	if matchData.Player2Points != nil && *matchData.Player2Points != "" {
		playerId2Points = pgtype.Int4{Int32: typeutils.ConvertToInt32(*matchData.Player2Points), Valid: true}
	}

	// Match date (use current if not provided)
	matchDate := currentMatch.MatchDate
	if matchData.MatchDate != nil {
		parsedDate, err := time.Parse("2006-01-02", *matchData.MatchDate)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "invalid date format: "+err.Error())
		}
		matchDate = pgtype.Date{Time: parsedDate, Valid: true}
	}

	// Match group (use current if not provided)
	matchGroup := currentMatch.MatchGroup
	if matchData.MatchGroup != nil {
		matchGroup = *matchData.MatchGroup
	}

	// Is active (use current if not provided)
	isActive := currentMatch.IsActive
	if matchData.IsActive != nil {
		isActive = *matchData.IsActive
	}

	// Update match in database
	updateParams := db.UpdateMatchParams{
		ID:              int32(matchId),
		SeasonID:        seasonId,
		PlayerId1:       playerId1,
		PlayerId1Points: playerId1Points,
		PlayerId2:       playerId2,
		PlayerId2Points: playerId2Points,
		MatchDate:       matchDate,
		MatchGroup:      matchGroup,
		IsActive:        isActive,
	}

	match, err := h.Queries.UpdateMatch(context.Background(), updateParams)
	if err != nil {
		// For HTMX requests, return an error response that won't break the UI
		if c.Request().Header.Get("HX-Request") == "true" {
			return c.String(http.StatusInternalServerError, "Update failed: "+err.Error())
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to update match: "+err.Error())
	}

	// Reschedule email reminders if email service is available
	if h.EmailService.IsEnabled() {
		go func() {
			// Get players with notifications enabled for this season
			players, err := h.Queries.GetPlayersWithNotificationsEnabled(context.Background(), match.SeasonID.Int32)
			if err != nil {
				return
			}

			// Convert to db.Player slice
			var dbPlayers []db.Player
			for _, p := range players {
				player := db.Player{
					ID:                        p.ID,
					Name:                      p.Name,
					Email:                     p.Email,
					EmailNotificationsEnabled: true,
					EmailReminderPreferences:  p.EmailReminderPreferences,
				}
				dbPlayers = append(dbPlayers, player)
			}

			// Reschedule reminders
			if err := h.EmailService.ScheduleMatchReminders(context.Background(), match, dbPlayers); err != nil {
				// Log error but don't fail the request
			}
		}()
	}

	// For HTMX requests, return updated row response
	if c.Request().Header.Get("HX-Request") == "true" {
		updatedMatches, err := h.Queries.GetMatchesBySeasonId(context.Background(), match.SeasonID)
		if err != nil {
			return c.String(http.StatusInternalServerError, "Failed to get updated match data")
		}

		// Find the specific match we just updated
		var targetMatch *db.GetMatchesBySeasonIdRow
		for _, m := range updatedMatches {
			if m.ID == match.ID {
				targetMatch = &m
				break
			}
		}

		if targetMatch == nil {
			return c.String(http.StatusInternalServerError, "Updated match not found")
		}

		// Get custom columns and values for the updated row
		customColumns, err := h.Queries.GetMatchCustomColumns(context.Background(), userID)
		if err != nil {
			return c.String(http.StatusInternalServerError, "Failed to fetch custom columns")
		}

		// Get custom values for this specific match
		customValues := make(map[int32][]db.MatchCustomValue)
		rows, err := h.Queries.GetMatchCustomValues(context.Background(), targetMatch.ID)
		if err == nil {
			var values []db.MatchCustomValue
			for _, row := range rows {
				values = append(values, db.MatchCustomValue{
					ID:        row.ID,
					MatchID:   row.MatchID,
					ColumnID:  row.ColumnID,
					Value:     row.Value,
					CreatedAt: row.CreatedAt,
					UpdatedAt: row.UpdatedAt,
				})
			}
			customValues[targetMatch.ID] = values
		}

		// Get language from cookie
		lang := cookies.GetLanguageFromCookie(c)

		hx.SetTrigger(c, hx.EventCloseAllModals, nil)

		// Use updated row template response
		response := matchrow.MatchRow(matchrow.MatchRowProps{
			Match:         *targetMatch,
			CustomColumns: customColumns,
			CustomValues:  customValues,
			Lang:          lang,
		})
		writer := c.Response().Writer
		return response.Render(writer)
	}

	return c.JSON(http.StatusOK, match)
}

// DeleteMatch handles DELETE /app/matches/:matchId
func (h *MatchesHandler) DeleteMatch(c echo.Context) error {
	// Get user ID from context
	userID, ok := c.Get(middleware.UserIDKey).(int32)
	if !ok {
		return echo.NewHTTPError(http.StatusUnauthorized, "invalid user ID")
	}

	// Parse matchId from URL
	matchIdStr := c.Param("matchId")
	matchId, err := strconv.Atoi(matchIdStr)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid match ID")
	}

	// Verify match exists and user owns the season
	existingMatch, err := h.Queries.GetMatch(context.Background(), int32(matchId))
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "match not found")
	}

	season, err := h.Queries.GetSeason(context.Background(), db.GetSeasonParams{
		ID:     existingMatch.SeasonID.Int32,
		UserID: pgtype.Int4{Int32: userID, Valid: true},
	})
	if err != nil {
		return echo.NewHTTPError(http.StatusForbidden, "access denied")
	}

	// Suppress unused variable warning
	_ = season

	// Delete match from database
	err = h.Queries.DeleteMatch(context.Background(), int32(matchId))
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to delete match: "+err.Error())
	}

	return c.NoContent(http.StatusNoContent)
}
