package templatesappseasons

import (
	"fmt"
	"maps"
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/dropdown"
	icons "github.com/j-em/coachpad/templates/ui/icons"
	matchrow "github.com/j-em/coachpad/templates/ui/matchRow"
	sortableheader "github.com/j-em/coachpad/templates/ui/sortableHeader"
	tablepagination "github.com/j-em/coachpad/templates/ui/tablePagination"
	"github.com/j-em/coachpad/utils"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// MatchesDashboardPageContent renders the main dashboard view for the Matches page.
func MatchesDashboardPageContent(props MatchesTableProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/matchesTable.locales.json", props.Lang)
	if err != nil {
		locales = map[string]string{}
	}

	// Load additional translations for season actions
	seasonLocales, err := i18n.LoadTemplateLocales("./templates/app/seasons/seasonDetailsPageContent.locales.json", props.Lang)
	if err == nil {
		maps.Copy(locales, seasonLocales)
	}

	return html.Div(
		html.Class("flex flex-col h-full"),
		html.ID("matches-container"),

		// Title header
		html.Div(
			html.Class("flex justify-between items-center mb-6"),
			html.H1(
				html.Class("text-2xl font-bold"),
				html.Data("testid", "season-matches-title"),
				gomponents.Text(props.SeasonName+" - "+locales["matches"]),
			),
		),

		// Table content container that gets refreshed
		MatchesTable(props),
	)
}

// MatchesTableContent renders just the refreshable table content (for HTMX updates)
func MatchesTableContent(props MatchesTableProps) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/matchesTable.locales.json", props.Lang)
	if err != nil {
		locales = map[string]string{}
	}

	// Load additional translations for season actions
	seasonLocales, err := i18n.LoadTemplateLocales("./templates/app/seasons/seasonDetailsPageContent.locales.json", props.Lang)
	if err == nil {
		maps.Copy(locales, seasonLocales)
	}

	baseURL := fmt.Sprintf("/app/seasons/%d/matches-table", props.SeasonId)

	return html.Div(
		html.Class("flex flex-col min-h-0"),

		// Actions dropdown (now refreshable with search parameters)
		html.Div(
			html.Class("flex justify-end items-center mb-4"),
			dropdown.Dropdown(dropdown.DropdownConfig{
				ID:         "season-actions",
				DataTestID: "season-actions-dropdown",
				ButtonText: "Actions",
				ButtonIcon: icons.EllipsisVertical(),
				Items: []dropdown.DropdownItem{
					{
						Text:       locales["print_matches"],
						Icon:       icons.Printer(),
						DataTestID: "print-matches-button",
						OnClick:    buildPrintURL(fmt.Sprintf("/app/seasons/%d", props.SeasonId), props.Params),
					},
					{
						Text:         locales["export_csv"],
						Icon:         icons.ArrowDownTray(),
						DataTestID:   "export-csv-button",
						Href:         "/app/seasons/" + strconv.Itoa(int(props.SeasonId)) + "/export/csv",
						DisableBoost: true,
					},
					{
						Text:       locales["get_public_link"],
						Icon:       icons.Link(),
						DataTestID: "public-schedule-link-button",
						XHxGet:     "'/app/seasons/" + strconv.Itoa(int(props.SeasonId)) + "/public-link'",
						HxTarget:   "#toast-body-container",
						HxSwap:     "afterbegin",
						OnClick:    "isOpen = false",
					},
				},
			}),
		),

		// Global saving indicator
		html.Div(
			html.Class("mb-4 text-sm text-gray-600 dark:text-gray-300 items-center htmx-indicator"),
			html.ID("global-saving-indicator"),
			gomponents.Attr("data-testid", "global-saving-indicator"),
			gomponents.El("svg",
				html.Class("animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500 dark:text-blue-400"),
				gomponents.Attr("xmlns", "http://www.w3.org/2000/svg"),
				gomponents.Attr("fill", "none"),
				gomponents.Attr("viewBox", "0 0 24 24"),
				gomponents.El("circle",
					html.Class("opacity-25"),
					gomponents.Attr("cx", "12"),
					gomponents.Attr("cy", "12"),
					gomponents.Attr("r", "10"),
					gomponents.Attr("stroke", "currentColor"),
					gomponents.Attr("stroke-width", "4"),
				),
			),
			gomponents.Text(locales["saving"]),
		),

		// Portal container for teleported date pickers
		html.Div(
			html.ID("datepicker-portal"),
			html.Class("relative"),
			gomponents.Attr("style", "z-index: 9999;"), // Ensure it's above everything
		),

		// Table with sticky header
		html.Div(
			html.Class("shadow border-b border-gray-200 dark:border-gray-700 sm:rounded-lg overflow-y-auto overflow-x-auto flex-1 min-h-0 bg-white dark:bg-gray-900"),
			html.Table(
				html.Class("min-w-full divide-y divide-gray-200 dark:divide-gray-700"),
				// Table Header with sticky positioning and sorting
				html.THead(
					html.Class("bg-gray-50 dark:bg-gray-800 sticky top-0 z-10"),
					html.Tr(
						sortableheader.SortableHeader(sortableheader.SortableHeaderProps{
							Field: "matchDate", Title: locales["table_header_date"], CurrentSort: props.Params.Sort, Direction: props.Params.Direction, BaseURL: baseURL, Search: props.Params.Search, FilterToday: props.Params.FilterToday, ItemsPerPage: props.Params.ItemsPerPage,
						}),
						sortableheader.SortableHeader(sortableheader.SortableHeaderProps{
							Field: "player_id1", Title: locales["table_header_player1"], CurrentSort: props.Params.Sort, Direction: props.Params.Direction, BaseURL: baseURL, Search: props.Params.Search, FilterToday: props.Params.FilterToday, ItemsPerPage: props.Params.ItemsPerPage,
						}),
						sortableheader.SortableHeader(sortableheader.SortableHeaderProps{
							Field: "playerId1Points", Title: locales["table_header_points1"], CurrentSort: props.Params.Sort, Direction: props.Params.Direction, BaseURL: baseURL, Search: props.Params.Search, FilterToday: props.Params.FilterToday, ItemsPerPage: props.Params.ItemsPerPage,
						}),
						sortableheader.SortableHeader(sortableheader.SortableHeaderProps{
							Field: "player_id2", Title: locales["table_header_player2"], CurrentSort: props.Params.Sort, Direction: props.Params.Direction, BaseURL: baseURL, Search: props.Params.Search, FilterToday: props.Params.FilterToday, ItemsPerPage: props.Params.ItemsPerPage,
						}),
						sortableheader.SortableHeader(sortableheader.SortableHeaderProps{
							Field: "playerId2Points", Title: locales["table_header_points2"], CurrentSort: props.Params.Sort, Direction: props.Params.Direction, BaseURL: baseURL, Search: props.Params.Search, FilterToday: props.Params.FilterToday, ItemsPerPage: props.Params.ItemsPerPage,
						}),
						sortableheader.SortableHeader(sortableheader.SortableHeaderProps{
							Field: "matchGroup", Title: locales["table_header_group"], CurrentSort: props.Params.Sort, Direction: props.Params.Direction, BaseURL: baseURL, Search: props.Params.Search, FilterToday: props.Params.FilterToday, ItemsPerPage: props.Params.ItemsPerPage,
						}),
						// Custom columns
						gomponents.Group(
							gomponents.Map(props.CustomColumns, func(column db.MatchCustomColumn) gomponents.Node {
								if !column.IsActive.Bool {
									return gomponents.Text("")
								}
								return html.Th(
									gomponents.Attr("scope", "col"),
									html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
									gomponents.Text(column.Name),
								)
							}),
						),
					),
				),
				// Table Body
				html.TBody(
					html.Class("bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700"),
					gomponents.Group(
						gomponents.Map(props.Matches, func(match db.GetMatchesBySeasonIdRow) gomponents.Node {
							return matchrow.MatchRow(matchrow.MatchRowProps{
								Match:         match,
								CustomColumns: props.CustomColumns,
								CustomValues:  props.CustomValues,
								Lang:          props.Lang,
							})
						}),
					),
				),
			),
		),

		// Add New Match Button
		button.PrimaryButton(button.BaseButtonConfig{
			ButtonType: "button",
			Text:       locales["add_match"],
			Class:      "mt-4",
			// TODO: Add HTMX attributes when functionality is implemented
		}),

		// Pagination Controls
		tablepagination.TablePagination(tablepagination.TablePaginationProps{
			Params:      props.Params.SortablePaginationParams,
			TotalItems:  props.TotalItems,
			TotalPages:  props.TotalPages,
			Lang:        props.Lang,
			BaseURL:     fmt.Sprintf("/app/seasons/%d/matches-table", props.SeasonId),
			DataTestID:  "matches-pagination",
			TargetID:    "matches-table-content",
			HtmxInclude: "[name='search'], [name='sort'], [name='dir'], [name='filter_today']",
			ExtraParams: func() map[string]string {
				extraParams := make(map[string]string)
				if props.Params.FilterToday {
					extraParams["filter_today"] = "true"
				}
				return extraParams
			}(),
		}),
	)
}

// MatchesTable renders the complete matches table with search bar and refreshable content
func MatchesTable(props MatchesTableProps) gomponents.Node {
	return html.Div(
		html.Class("flex flex-col min-h-0"),

		// Refreshable table content container
		html.Div(
			html.ID("matches-table-content"),
			html.Class("flex flex-col min-h-0"),
			MatchesTableContent(props),
		),
	)
}

// buildPrintURL creates a JavaScript window.open call with current filter state
func buildPrintURL(baseURL string, params MatchesTableParams) string {
	// Debug logging to see what parameters we're receiving
	fmt.Printf("DEBUG buildPrintURL: Search='%s', Page=%d, Sort='%s', Dir='%s', FilterToday=%t\n",
		params.Search, params.Page, params.Sort, params.Direction, params.FilterToday)

	printParams := utils.PrintURLParams{
		BaseURL:      baseURL,
		Page:         params.Page,
		ItemsPerPage: params.ItemsPerPage,
		Search:       params.Search,
		Sort:         params.Sort,
		Direction:    params.Direction,
		ExtraParams:  make(map[string]string),
	}

	if params.FilterToday {
		printParams.ExtraParams["filter_today"] = "true"
	}

	url := utils.BuildPrintURL(printParams)
	fmt.Printf("DEBUG buildPrintURL: Generated URL='%s'\n", url)
	return url
}
