package templatesappseasons

import (
	"strconv"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/templates/ui/button"
	"github.com/j-em/coachpad/templates/ui/form"
	"github.com/j-em/coachpad/templates/ui/icons"
	"github.com/j-em/coachpad/templates/ui/modal"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

type RenameModalProps struct {
	SeasonID   int32
	SeasonName string
	Lang       string
}

func RenameModal(props RenameModalProps) gomponents.Node {
	// Load locales with error handling and fallback
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/renameModal.locales.json", props.Lang)
	if err != nil {
		locales = map[string]string{
			"title":      "Rename Season",
			"name_label": "Season Name",
			"save":       "Save",
			"cancel":     "Cancel",
		}
	}

	seasonIdStr := strconv.Itoa(int(props.SeasonID))

	formContent := html.Div(
		html.Class("space-y-4"),
		form.FormInput(form.FormInputProps{
			Label:    locales["name_label"],
			Type:     "text",
			ID:       "season-name",
			Name:     "name",
			Required: true,
			Value:    props.SeasonName,
		}),
		html.Div(
			html.Class("flex justify-end space-x-3 pt-4"),
			button.SecondaryButton(button.BaseButtonConfig{
				Text:       locales["cancel"],
				ButtonType: "button",
				Attrs: []gomponents.Node{
					gomponents.Attr("@click", "renameSeasonModal_modalOpen = false"),
				},
			}),
			button.PrimaryIconButton(button.IconButtonConfig{
				Text:       locales["save"],
				ButtonType: "submit",
				Icon:       icons.CheckCircle(),
				DataTestID: "save-season-name",
			}),
		),
	)

	// Using a custom wrapper since this needs specific form action
	// but following the auto-open pattern with standardized form handling
	formWithHTMX := html.Form(
		htmx.Put("/app/seasons/"+seasonIdStr),
		htmx.Target("#toast-body-container"),
		htmx.Swap("afterbegin"),
		gomponents.Attr("@htmx:after-request", "if(event.detail.successful) { renameSeasonModal_modalOpen = false }"),
		formContent,
	)

	return modal.ClientModalWithAutoOpen(modal.ClientModalConfig{
		ModalID:      "renameSeasonModal",
		Title:        locales["title"],
		Content:      formWithHTMX,
		IsUnclosable: false,
		DataTestID:   "rename-season-modal",
	})
}
