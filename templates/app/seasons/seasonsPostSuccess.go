package templatesappseasons

import (
	"fmt"

	"github.com/j-em/coachpad/i18n"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// SeasonsPostSuccess renders a success message fragment that can be inserted into the season page.
func SeasonsPostSuccess(seasonId string, lang string) gomponents.Node {
	locales, err := i18n.LoadTemplateLocales("./templates/app/seasons/seasonsPostSuccess.locales.json", lang)
	if err != nil {
		// Handle error, use default values
		locales = make(map[string]string)
	}

	return html.Div(
		html.Class("bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4"),
		html.Role("alert"),
		html.Div(
			html.Class("flex flex-col space-y-2"),
			html.Span(
				html.Class("block sm:inline"),
				gomponents.Text(locales["successMessage"]),
			),
			html.A(
				html.Href(fmt.Sprintf("/app/seasons/%s", seasonId)),
				html.Class("inline-flex items-center px-4 py-2 bg-green-700 hover:bg-green-800 text-white text-sm font-medium rounded-md"),
				gomponents.Text(locales["viewSeason"]),
			),
		),
	)
}
