package templatesnav

import (
	"github.com/j-em/coachpad/templates/ui/dropdown"
	gomponents "maragu.dev/gomponents"
	heroicons "maragu.dev/gomponents-heroicons/v3/solid"
)

// NavHelpDropdown renders the options dropdown menu with settings and help items
func NavHelpDropdown(locales map[string]string) gomponents.Node {
	return dropdown.Dropdown(dropdown.DropdownConfig{
		ID:         "help-dropdown",
		DataTestID: "help-dropdown",
		ButtonIcon: heroicons.EllipsisHorizontalCircle(),
		Class:      "w-full ml-auto",
		Position:   dropdown.PositionTop,
		Items: []dropdown.DropdownItem{
			{
				Text:       locales["help"],
				Icon:       heroicons.QuestionMarkCircle(),
				Href:       "/app/help",
				DataTestID: "help-center-button",
			},
			{
				Text:       locales["user_settings"],
				Icon:       heroicons.UserCircle(),
				Href:       "/app/settings/user",
				DataTestID: "user-settings-button",
			},
			{
				Text:       locales["app_settings"],
				Icon:       heroicons.Cog(),
				Href:       "/app/settings/app",
				DataTestID: "app-settings-button",
			},
			{
				Text:       locales["subscription"],
				Icon:       heroicons.CreditCard(),
				Href:       "/app/settings/subscription",
				DataTestID: "subscription-settings-button",
			},
			{
				Text:       locales["api_access"],
				Icon:       heroicons.Key(),
				Href:       "/app/settings/api-access",
				DataTestID: "api-access-settings-button",
			},
			{
				Text:       locales["changelog"],
				Icon:       heroicons.DocumentText(),
				XHxGet:     "'/app/changelog'",
				HxTarget:   "#modal-body-container",
				DataTestID: "changelog-button",
			},
			{
				Text:       locales["give_feedback"],
				Icon:       heroicons.ChatBubbleBottomCenterText(),
				XHxGet:     "'/app/feedback'",
				HxTarget:   "#modal-body-container",
				DataTestID: "give-feedback-button",
			},
		},
	})
}
