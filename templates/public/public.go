package public

import (
	"strconv"

	"github.com/j-em/coachpad/db"
	"github.com/j-em/coachpad/i18n"
	templatescomponents "github.com/j-em/coachpad/templates/components"
	"github.com/j-em/coachpad/templates/layouts"
	"github.com/j-em/coachpad/utils/matchutils"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// PublicSeasonSchedulePage renders the public schedule for a specific season
func PublicSeasonSchedulePage(lang string, season db.Season, matches []db.GetMatchesBySeasonIdRow, customColumns []db.MatchCustomColumn) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/public/public.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Load header locales for signin/signup buttons
	headerLocales, err := i18n.LoadTemplateLocales("./templates/components/public_header.locales.json", lang)
	if err != nil {
		headerLocales = make(map[string]string)
	}

	pageTitle := season.Name
	if pageTitle == "" {
		pageTitle = locales["schedule_title"]
	}

	return layouts.HTMLLayout(pageTitle, html.Div(
		html.Class("min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300"),
		templatescomponents.PublicHeader(lang, html.Div(
			html.Class("flex items-center gap-2 flex-col md:flex-row w-full md:w-auto"),
			templatescomponents.PublicHeaderButton(headerLocales["sign_in"], "/signin"),
			templatescomponents.PublicHeaderButton(headerLocales["sign_up"], "/signup"),
		)),
		templatescomponents.PublicNav(templatescomponents.PublicNavConfig{
			CurrentPath: "/public/seasons",
			SeasonId:    strconv.Itoa(int(season.ID)),
		}),
		html.Main(
			html.Class("container mx-auto px-4 py-8"),
			html.Div(
				html.Class("bg-white dark:bg-gray-800 rounded-lg shadow p-6"),
				html.H1(
					html.Class("text-3xl font-bold text-gray-900 dark:text-white mb-6"),
					gomponents.Text(season.Name),
				),
				html.P(
					html.Class("text-gray-600 dark:text-gray-300 mb-8"),
					gomponents.Text(locales["season_schedule_description"]),
				),
				PublicMatchesTable(matches, customColumns, locales),
			),
		),
	))
}

// PublicScheduleIndexPage renders the index page with navigation to schedules
func PublicScheduleIndexPage(lang string) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/public/public.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Load header locales for signin/signup buttons
	headerLocales, err := i18n.LoadTemplateLocales("./templates/components/public_header.locales.json", lang)
	if err != nil {
		headerLocales = make(map[string]string)
	}

	return layouts.HTMLLayout(locales["schedule_index_title"], html.Div(
		html.Class("min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300"),
		templatescomponents.PublicHeader(lang, html.Div(
			html.Class("flex items-center gap-2 flex-col md:flex-row w-full md:w-auto"),
			templatescomponents.PublicHeaderButton(headerLocales["sign_in"], "/signin"),
			templatescomponents.PublicHeaderButton(headerLocales["sign_up"], "/signup"),
		)),
		templatescomponents.PublicNav(templatescomponents.PublicNavConfig{
			CurrentPath: "/public/schedule",
			SeasonId:    "",
		}),
		html.Main(
			html.Class("container mx-auto px-4 py-8"),
			html.Div(
				html.Class("bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center"),
				html.H1(
					html.Class("text-3xl font-bold text-gray-900 dark:text-white mb-6"),
					gomponents.Text(locales["schedule_index_title"]),
				),
				html.P(
					html.Class("text-gray-600 dark:text-gray-300 mb-8"),
					gomponents.Text(locales["schedule_index_description"]),
				),
				html.P(
					html.Class("text-sm text-gray-500 dark:text-gray-400"),
					gomponents.Text(locales["access_via_link"]),
				),
			),
		),
	))
}

// PublicScoreboardIndexPage renders the scoreboard index page
func PublicScoreboardIndexPage(lang string) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/public/public.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Load header locales for signin/signup buttons
	headerLocales, err := i18n.LoadTemplateLocales("./templates/components/public_header.locales.json", lang)
	if err != nil {
		headerLocales = make(map[string]string)
	}

	return layouts.HTMLLayout(locales["scoreboard_index_title"], html.Div(
		html.Class("min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300"),
		templatescomponents.PublicHeader(lang, html.Div(
			html.Class("flex items-center gap-2 flex-col md:flex-row w-full md:w-auto"),
			templatescomponents.PublicHeaderButton(headerLocales["sign_in"], "/signin"),
			templatescomponents.PublicHeaderButton(headerLocales["sign_up"], "/signup"),
		)),
		templatescomponents.PublicNav(templatescomponents.PublicNavConfig{
			CurrentPath: "/public/scoreboard",
			SeasonId:    "",
		}),
		html.Main(
			html.Class("container mx-auto px-4 py-8"),
			html.Div(
				html.Class("bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center"),
				html.H1(
					html.Class("text-3xl font-bold text-gray-900 dark:text-white mb-6"),
					gomponents.Text(locales["scoreboard_index_title"]),
				),
				html.P(
					html.Class("text-gray-600 dark:text-gray-300 mb-8"),
					gomponents.Text(locales["scoreboard_index_description"]),
				),
				html.P(
					html.Class("text-sm text-gray-500 dark:text-gray-400"),
					gomponents.Text(locales["access_via_link"]),
				),
			),
		),
	))
}

// PublicSeasonScoreboardPage renders the public scoreboard for a specific season
func PublicSeasonScoreboardPage(lang string, season db.Season, scoreboardData []db.GetSeasonScoreboardRow) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/public/public.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	// Load header locales for signin/signup buttons
	headerLocales, err := i18n.LoadTemplateLocales("./templates/components/public_header.locales.json", lang)
	if err != nil {
		headerLocales = make(map[string]string)
	}

	pageTitle := season.Name + " - " + locales["scoreboard_title"]
	if season.Name == "" {
		pageTitle = locales["scoreboard_title"]
	}

	return layouts.HTMLLayout(pageTitle, html.Div(
		html.Class("min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300"),
		templatescomponents.PublicHeader(lang, html.Div(
			html.Class("flex items-center gap-2 flex-col md:flex-row w-full md:w-auto"),
			templatescomponents.PublicHeaderButton(headerLocales["sign_in"], "/signin"),
			templatescomponents.PublicHeaderButton(headerLocales["sign_up"], "/signup"),
		)),
		templatescomponents.PublicNav(templatescomponents.PublicNavConfig{
			CurrentPath: "/public/scoreboard",
			SeasonId:    strconv.Itoa(int(season.ID)),
		}),
		html.Main(
			html.Class("container mx-auto px-4 py-8"),
			html.Div(
				html.Class("bg-white dark:bg-gray-800 rounded-lg shadow p-6"),
				html.H1(
					html.Class("text-3xl font-bold text-gray-900 dark:text-white mb-6"),
					gomponents.Text(season.Name+" - "+locales["scoreboard_title"]),
				),
				html.P(
					html.Class("text-gray-600 dark:text-gray-300 mb-8"),
					gomponents.Text(locales["season_scoreboard_description"]),
				),
				PublicScoreboardTable(scoreboardData, locales),
			),
		),
	))
}

// PublicMatchesTable renders a table of matches for public viewing
func PublicMatchesTable(matches []db.GetMatchesBySeasonIdRow, customColumns []db.MatchCustomColumn, locales map[string]string) gomponents.Node {
	if len(matches) == 0 {
		return html.Div(
			html.Class("text-center py-8"),
			html.P(
				html.Class("text-gray-500 dark:text-gray-400"),
				gomponents.Text(locales["no_matches"]),
			),
		)
	}

	return html.Div(
		html.Class("overflow-x-auto"),
		html.Table(
			html.Class("min-w-full divide-y divide-gray-200 dark:divide-gray-700"),
			html.THead(
				html.Class("bg-gray-50 dark:bg-gray-700"),
				html.Tr(
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text(locales["match_date"]),
					),
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text(locales["player1"]),
					),
					html.Th(
						html.Class("px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text(locales["score"]),
					),
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text(locales["player2"]),
					),
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text(locales["group"]),
					),
					// Add custom columns headers
					gomponents.Group(func() []gomponents.Node {
						var headers []gomponents.Node
						for _, col := range customColumns {
							headers = append(headers, html.Th(
								html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
								gomponents.Text(col.Name),
							))
						}
						return headers
					}()),
				),
			),
			html.TBody(
				html.Class("bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"),
				gomponents.Group(func() []gomponents.Node {
					var rows []gomponents.Node
					for _, match := range matches {
						rows = append(rows, PublicMatchRow(match, customColumns, locales))
					}
					return rows
				}()),
			),
		),
	)
}

// PublicScoreboardTable renders the scoreboard table
func PublicScoreboardTable(scoreboardData []db.GetSeasonScoreboardRow, locales map[string]string) gomponents.Node {
	if len(scoreboardData) == 0 {
		return html.Div(
			html.Class("text-center py-8"),
			html.P(
				html.Class("text-gray-500 dark:text-gray-400"),
				gomponents.Text(locales["no_players"]),
			),
		)
	}

	return html.Div(
		html.Class("overflow-x-auto"),
		html.Table(
			html.Class("min-w-full divide-y divide-gray-200 dark:divide-gray-700"),
			html.THead(
				html.Class("bg-gray-50 dark:bg-gray-700"),
				html.Tr(
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text(locales["rank"]),
					),
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text(locales["player_name"]),
					),
					html.Th(
						html.Class("px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"),
						gomponents.Text(locales["wins"]),
					),
				),
			),
			html.TBody(
				html.Class("bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"),
				gomponents.Group(func() []gomponents.Node {
					var rows []gomponents.Node
					for i, player := range scoreboardData {
						rows = append(rows, PublicScoreboardRow(i+1, player, locales))
					}
					return rows
				}()),
			),
		),
	)
}

// PublicMatchRow renders a single match row
func PublicMatchRow(match db.GetMatchesBySeasonIdRow, customColumns []db.MatchCustomColumn, locales map[string]string) gomponents.Node {
	player1Name := matchutils.FormatPlayerNameSimple(match.Player1Name, locales["unknown_player"])
	player2Name := matchutils.FormatPlayerNameSimple(match.Player2Name, locales["unknown_player"])
	score := matchutils.FormatScore(match.PlayerId1Points, match.PlayerId2Points)
	matchGroup := matchutils.FormatMatchGroup(match.MatchGroup)

	return html.Tr(
		html.Class("hover:bg-gray-50 dark:hover:bg-gray-700"),
		html.Td(
			html.Class("px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"),
			gomponents.Text(matchutils.FormatMatchDate(match.MatchDate, matchutils.DateFormatYMDTime)),
		),
		html.Td(
			html.Class("px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"),
			gomponents.Text(player1Name),
		),
		html.Td(
			html.Class("px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white text-center"),
			gomponents.Text(score),
		),
		html.Td(
			html.Class("px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"),
			gomponents.Text(player2Name),
		),
		html.Td(
			html.Class("px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300"),
			gomponents.Text(matchGroup),
		),
		// Add custom column values (placeholder for now)
		gomponents.Group(func() []gomponents.Node {
			var cells []gomponents.Node
			for range customColumns {
				cells = append(cells, html.Td(
					html.Class("px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300"),
					gomponents.Text("-"),
				))
			}
			return cells
		}()),
	)
}

// PublicScoreboardRow renders a single scoreboard row
func PublicScoreboardRow(rank int, player db.GetSeasonScoreboardRow, locales map[string]string) gomponents.Node {
	rankColor := "text-gray-900 dark:text-white"
	if rank == 1 {
		rankColor = "text-yellow-600 font-bold" // Gold for 1st place
	} else if rank == 2 {
		rankColor = "text-gray-500 font-bold" // Silver for 2nd place
	} else if rank == 3 {
		rankColor = "text-amber-600 font-bold" // Bronze for 3rd place
	}

	return html.Tr(
		html.Class("hover:bg-gray-50 dark:hover:bg-gray-700"),
		html.Td(
			html.Class("px-6 py-4 whitespace-nowrap text-sm "+rankColor),
			gomponents.Text("#"+strconv.Itoa(rank)),
		),
		html.Td(
			html.Class("px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"),
			gomponents.Text(player.PlayerName),
		),
		html.Td(
			html.Class("px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"),
			gomponents.Text(strconv.FormatInt(player.Wins, 10)),
		),
	)
}
