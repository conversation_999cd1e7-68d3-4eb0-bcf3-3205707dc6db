package dropdown

import (
	"fmt"
	"strconv"

	"github.com/j-em/coachpad/templates/ui/button"
	"maragu.dev/gomponents"
	htmx "maragu.dev/gomponents-htmx"
	"maragu.dev/gomponents/html"
)

type DropdownPosition string

const (
	PositionBottom DropdownPosition = "bottom" // default
	PositionTop    DropdownPosition = "top"
	PositionLeft   DropdownPosition = "left"
	PositionRight  DropdownPosition = "right"
)

type DropdownItem struct {
	Text         string
	Href         string
	Icon         gomponents.Node
	Shortcut     string
	Disabled     bool
	OnClick      string
	XOnClick     string // Added x-bind:@click support
	XHxGet       string // Renamed from HxGet: supports x-bind:hx-get
	HxTarget     string
	HxSwap       string // Added support for hx-swap
	XHref        string // Added x-bind:href support
	DataTestID   string // Optional data-testid attribute
	XDataTestID  string // Optional x-bind:data-testid attribute
	DisableBoost bool   // Disable HTMX boost for this item
}

type DropdownConfig struct {
	ID          string
	DataTestID  string
	ButtonText  string
	XButtonText string // Added: x-text binding for dynamic button text
	ButtonIcon  gomponents.Node
	Items       []DropdownItem
	Class       string
	Position    DropdownPosition // Menu attachment position

	XID         string
	XDataTestID string
	XClass      string
}

func Dropdown(config DropdownConfig) gomponents.Node {
	dropdownClasses := "z-40 w-56 p-1 bg-white border rounded-md shadow-md border-neutral-200/70 text-neutral-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-200"

	// Default position to bottom if not specified
	position := config.Position
	if position == "" {
		position = PositionBottom
	}

	// Use BaseButton for dropdown items
	items := make([]gomponents.Node, len(config.Items))
	for i, item := range config.Items {
		itemClasses := "relative flex cursor-pointer select-none items-center rounded px-2 py-1.5 text-sm outline-none transition-colors hover:bg-neutral-100 dark:hover:bg-gray-700 w-full"
		if item.Disabled {
			itemClasses += " pointer-events-none opacity-50"
		}

		// Compose the content for the button
		content := []gomponents.Node{
			html.Div(
				html.Class("flex items-center"),
				html.Div(
					html.Class("w-4 h-4 mr-2"),
					item.Icon,
				),
				html.Span(
					html.Class("flex-1 truncate"), // Added flex-1 to make text take available space
					gomponents.Text(item.Text),
				),
			),
		}
		if item.Shortcut != "" {
			content = append(content, html.Span(
				html.Class("ml-auto text-xs tracking-widest opacity-60"),
				gomponents.Text(item.Shortcut),
			))
		}

		// Create dropdown item as anchor or button based on whether Href is provided
		var dropdownItem gomponents.Node

		if item.Href != "" {
			// Use anchor tag for items with href
			attrs := []gomponents.Node{
				html.Href(item.Href),
				html.Class(itemClasses),
			}

			// Only add boost if not explicitly disabled
			if !item.DisableBoost {
				attrs = append(attrs, htmx.Boost("true"))
			}

			if item.DataTestID != "" {
				attrs = append(attrs, gomponents.Attr("data-testid", item.DataTestID))
			}
			if item.XDataTestID != "" {
				attrs = append(attrs, gomponents.Attr("x-bind:data-testid", item.XDataTestID))
			}
			if item.OnClick != "" {
				attrs = append(attrs, gomponents.Attr("@click", item.OnClick))
			}
			if item.XHref != "" {
				attrs = append(attrs, gomponents.Attr("x-bind:href", item.XHref))
			}

			// Add the content as children
			attrs = append(attrs, gomponents.Group(content))

			dropdownItem = html.A(attrs...)
		} else {
			// Use button for items without href
			attrs := []gomponents.Node{
				htmx.Boost("true"),
			}

			if item.XHxGet != "" {
				attrs = append(attrs, gomponents.Attr("x-bind:hx-get", item.XHxGet))
			}
			if item.HxTarget != "" {
				attrs = append(attrs, htmx.Target(item.HxTarget))
			}
			if item.HxSwap != "" {
				attrs = append(attrs, htmx.Swap(item.HxSwap))
			}
			if item.XHref != "" {
				attrs = append(attrs, gomponents.Attr("x-bind:href", item.XHref))
			}
			if item.OnClick != "" {
				attrs = append(attrs, gomponents.Attr("@click", item.OnClick))
			}
			if item.Disabled {
				attrs = append(attrs, gomponents.Attr("disabled", "true"))
			}

			// Add the content as children
			attrs = append(attrs, gomponents.Group(content))

			btnConfig := button.BaseButtonConfig{
				ButtonType: "button",
				ID:         "dropdown-item-" + strconv.Itoa(i),
				Text:       "", // content is handled via Attrs
				Class:      itemClasses,
				Attrs:      attrs,
			}
			if item.DataTestID != "" {
				btnConfig.DataTestID = item.DataTestID
			}
			if item.XDataTestID != "" {
				btnConfig.XDataTestID = item.XDataTestID
			}
			dropdownItem = button.BaseButton(btnConfig)
		}

		items[i] = dropdownItem
	}

	// Build attribute list only if config fields are set
	var nodes []gomponents.Node
	nodes = append(nodes, html.ID("dropdown-"+config.ID))
	if config.DataTestID != "" {
		nodes = append(nodes, gomponents.Attr("data-testid", config.DataTestID))
	}
	className := "relative"
	if config.Class != "" {
		className += " " + config.Class
	}
	nodes = append(nodes, html.Class(className))
	if config.XID != "" {
		nodes = append(nodes, gomponents.Attr("x-bind:id", config.XID))
	}
	if config.XDataTestID != "" {
		nodes = append(nodes, gomponents.Attr("x-bind:data-testid", config.XDataTestID))
	}
	if config.XClass != "" {
		nodes = append(nodes, gomponents.Attr("x-bind:class", config.XClass))
	}
	// Alpine state for open/close and menu positioning
	nodes = append(nodes,
		gomponents.Attr(`x-data`, fmt.Sprintf("dropdownComponent('%s')", string(position))),
		gomponents.Attr(`@close-all-dropdowns.window`, `closeAllDropdowns($event)`),
	)
	// Button and menu group
	nodes = append(nodes, gomponents.Group([]gomponents.Node{
		button.IconButton(button.IconButtonConfig{
			ID:         "dropdown-trigger-" + config.ID,
			ButtonType: "button",
			Text:       config.ButtonText,
			Class:      "bg-white border text-neutral-700 hover:bg-neutral-100 active:bg-white focus:bg-white dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700 dark:active:bg-gray-800 dark:focus:bg-gray-800 dark:border-gray-600 transition-colors",
			Icon:       config.ButtonIcon,
			Size:       "small",
			Attrs: []gomponents.Node{
				gomponents.Attr("@click", "isOpen = !isOpen; if (isOpen) { positionCalculated = false; $nextTick(() => updateMenuPosition()); $dispatch('dropdown-opened', { id: '"+config.ID+"' }); } else { $dispatch('dropdown-closed', { id: '"+config.ID+"' }); }"),
				gomponents.Attr("x-ref", "button"),
				gomponents.If(config.XButtonText != "",
					gomponents.Attr("x-text", config.XButtonText),
				),
			},
		}),
		html.Template(
			gomponents.Attr("x-teleport", "body"),
			html.Div(
				html.Class(dropdownClasses),
				gomponents.Attr("x-ref", "menu"),
				gomponents.Attr("x-init", "$nextTick(() => htmx.process($el))"),
				gomponents.Attr("x-show", "isOpen"),
				gomponents.Attr("x-transition:enter", "transition ease-out duration-300"),
				gomponents.Attr("x-transition:enter-start", fmt.Sprintf("opacity-0 scale-95 %s",
					func() string {
						if position == PositionTop {
							return "translate-y-2" // For top position, start from below
						}
						return "-translate-y-2" // For bottom/others, start from above
					}())),
				gomponents.Attr("x-transition:enter-end", "opacity-100 scale-100 translate-y-0"),
				gomponents.Attr("x-transition:leave", "transition ease-in duration-200"),
				gomponents.Attr("x-transition:leave-start", "opacity-100 scale-100 translate-y-0"),
				gomponents.Attr("x-transition:leave-end", fmt.Sprintf("opacity-0 scale-95 %s",
					func() string {
						if position == PositionTop {
							return "translate-y-2" // For top position, exit to below
						}
						return "-translate-y-2" // For bottom/others, exit to above
					}())),
				gomponents.Attr("@click.away", "isOpen = false; $dispatch('dropdown-closed', { id: '"+config.ID+"' })"),
				gomponents.Attr(":style", "menuStyles"),
				gomponents.Group(items),
			),
		),
	}))
	return html.Div(nodes...)
}
