package layouts

import (
	"os"
	"strings"

	"golang.org/x/net/html"

	"github.com/j-em/coachpad/assets"
	embedFS "github.com/j-em/coachpad/embed"
	"github.com/j-em/coachpad/templates/ui/cookieconsent"
	. "maragu.dev/gomponents"
	. "maragu.dev/gomponents/components"
	. "maragu.dev/gomponents/html"
)

// HTMLLayout represents the base HTML structure for the application.
func HTMLLayout(title string, content Node) Node {
	isDev := os.Getenv("IS_DEV") == "true"
	distFS := embedFS.GetDistFS()
	viteFragment := assets.GetViteFragment(isDev, distFS)
	return HTML5(HTML5Props{
		Language: "en",
		Title:    title,
		Head: []Node{
			Meta(Charset("UTF-8")),
			// Favicon
			El("link",
				Attr("rel", "icon"),
				Attr("type", "image/svg+xml"),
				Attr("href", "/images/favicon.ico"),
			),
			Raw(ModifyViteTags(string(viteFragment.Tags))),
			Title(title),
		},
		Body: []Node{
			Attr("hx-ext", "head-support,sse"),
			Group([]Node{
				Div(
					ID("modal-body-container"),
				),
				Div(
					ID("toast-body-container"),
					Class("absolute bottom-4 right-4 z-60 space-y-2"),
				),
				Div(
					ID("datepicker-portal"),
					Class("relative"),
					Attr("style", "z-index: 9999;"), // Ensure it's above everything
				),
				Main(content),
				cookieconsent.CookieConsentModal(cookieconsent.CookieConsentConfig{}),
			}),
		},
	})
}

// ModifyViteTags adds hx-preserve="true" to all link and script tags in the raw HTML string.
func ModifyViteTags(rawTags string) string {
	var result strings.Builder
	reader := strings.NewReader(rawTags)
	tokenizer := html.NewTokenizer(reader)

	for {
		tt := tokenizer.Next()
		if tt == html.ErrorToken {
			break
		}

		token := tokenizer.Token()
		switch tt {
		case html.StartTagToken, html.SelfClosingTagToken:
			if token.Data == "link" || token.Data == "script" {
				// Check if hx-preserve already exists
				hasPreserve := false
				for _, attr := range token.Attr {
					if attr.Key == "hx-preserve" {
						hasPreserve = true
						break
					}
				}

				if !hasPreserve {
					token.Attr = append(token.Attr, html.Attribute{
						Key: "hx-preserve",
						Val: "true",
					})
				}
			}
		}

		result.WriteString(token.String())
	}

	return result.String()
}
