import { expect, Page, Locator } from '@playwright/test';
import { defaultSeasonConfig, test } from "./seasonFixture";

// Sidebar seasons list scrollability test with 50+ seasons
test.describe('Nav Sidebar seasons list', () => {
  test.use({
    seasonConfig: {
      ...defaultSeasonConfig,
      count: 50, // Set to 50 seasons for this test
    }
  }); // Use 50 seasons for all tests in this block

  test('is scrollable when there are 50+ seasons', async ({ seasonFixture }) => {
    const { seasons, page } = seasonFixture;

    await page.goto('/app/home');

    const navBar: Locator = page.locator('[data-testid="sidebar-nav"]');
    const seasonsList: Locator = navBar.locator('[data-testid="sidebar-seasons-list"]');
    await expect(seasonsList).toBeVisible();
    const seasonCount: number = await seasonsList.locator('[data-testid^="nav-season-link-"]').count();
    expect(seasonCount).toBeGreaterThanOrEqual(50);
    const isScrollable: boolean = await seasonsList.evaluate(
      (el: HTMLElement) => el.scrollHeight > el.clientHeight
    );
    expect(isScrollable).toBe(true);
    // Optionally, check last season is visible after scroll
    const lastSeasonName: string = seasonFixture.seasons[seasonFixture.seasons.length - 1].name;
    await seasonsList.evaluate((el: HTMLElement) => { el.scrollTop = el.scrollHeight; });
    await expect(seasonsList.locator(`text=${lastSeasonName}`)).toBeVisible();
  });
});

test("should display the seasons list with actual seasons", async ({
  seasonFixture,
}) => {
  const { seasons, page } = seasonFixture;

  // Navigate to a page that uses the app layout
  await page.goto("/app/home");

  // Locate the navigation bar
  const navBar: Locator = page.locator('[data-testid="sidebar-nav"]');

  // Assert that the "Your Seasons" heading is visible within the navigation bar
  await expect(
    navBar.locator('h1:has-text("Seasons")'),
  ).toBeVisible();

  // Check that at least one of the seasons from our fixture appears in the nav
  const firstSeasonName: string = seasonFixture.seasons[0].name;
  await expect(navBar.locator(`text=${firstSeasonName}`)).toBeVisible();
});

test("sidebar toggle functionality should collapse and expand correctly", async ({
  seasonFixture,
  isolatedUser
}) => {
  const { page } = isolatedUser;
  await page.goto("/app/home");

  // Scope all locators to the sidebar and header
  const sidebar: Locator = page.locator('[data-testid="sidebar-nav"]');
  const collapseButton: Locator = sidebar.locator("[data-testid='nav-collapse-button']");
  const header: Locator = page.locator("header");
  const expandButton: Locator = header.locator("[data-testid='nav-expand-button']");

  // Ensure sidebar is initially visible
  await expect(sidebar).toBeVisible();

  // Test collapse functionality
  await collapseButton.click();
  await expect(sidebar).toBeHidden();

  // Test expand functionality
  await expandButton.click();
  
  // Assert that the sidebar is visible again and has proper width
  await expect(sidebar).toBeVisible();
  const sidebarWidth: number = await sidebar.evaluate((el: HTMLElement) => el.offsetWidth);
  expect(sidebarWidth).toBeGreaterThan(0);
});

test("on mobile, clicking nav link should close sidebar", async ({
  seasonFixture,
  isolatedUser
}) => {
  const { page } = isolatedUser;
  
  // Set mobile viewport
  await page.setViewportSize({ width: 375, height: 667 });
  await page.goto("/app/home");

  const mobileSidebar: Locator = page.locator('[data-testid="sidebar-nav"]');
  const header: Locator = page.locator("header");
  const expandButton: Locator = header.locator("[data-testid='nav-expand-button']");

  // Check if sidebar is already open or closed
  const isSidebarInitiallyVisible = await mobileSidebar.isVisible();
  
  if (!isSidebarInitiallyVisible) {
    // If sidebar is closed, open it first
    await expandButton.click();
    await expect(mobileSidebar).toBeVisible();
  }
  
  // Now sidebar should be visible, click on a nav link (players page)
  const playersLink: Locator = mobileSidebar.locator('a[href="/app/players"]');
  await playersLink.click();
  
  // Wait for navigation
  await page.waitForURL('/app/players');
  
  // Wait a bit for the close action to take effect
  await page.waitForTimeout(300);
  
  // Verify sidebar is now closed (hidden)
  await expect(mobileSidebar).toBeHidden();
});

test("help dropdown shows changelog option", async ({ seasonFixture }) => {
  const { page } = seasonFixture;
  
  await page.goto("/app/home");

  // Find the help dropdown button in the desktop sidebar (should be visible by default)
  const helpDropdown = page.locator('[data-testid="sidebar-nav"] [data-testid="help-dropdown"]');
  await expect(helpDropdown).toBeVisible();

  // Click the dropdown button to open it
  const dropdownButton = helpDropdown.locator('button');
  await dropdownButton.click();

  // Wait for Alpine.js to process the click and teleport the menu
  await page.waitForTimeout(100);

  // Find the teleported dropdown menu as a direct child of body
  const dropdownMenu = page.locator('body > div[x-ref="menu"]:visible');
  await expect(dropdownMenu).toBeVisible({ timeout: 5000 });

  // Verify the changelog option is visible in the opened dropdown
  const changelogOption = dropdownMenu.locator('text=Changelog').first();
  await expect(changelogOption).toBeVisible();

  // Click away to close the dropdown
  await page.click('body');
  await page.waitForTimeout(200);

  // Verify the dropdown menu is hidden (check that no dropdown is visible)
  await expect(dropdownMenu).toBeHidden();
});

test("options dropdown shows settings options", async ({ seasonFixture }) => {
  const { page } = seasonFixture;
  
  await page.goto("/app/home");

  // Find the options dropdown button in the desktop sidebar
  const optionsDropdown = page.locator('[data-testid="sidebar-nav"] [data-testid="help-dropdown"]');
  await expect(optionsDropdown).toBeVisible();

  // Click the dropdown button to open it
  const dropdownButton = optionsDropdown.locator('button');
  await dropdownButton.click();

  // Wait for Alpine.js to process the click and teleport the menu
  await page.waitForTimeout(100);

  // Find the teleported dropdown menu as a direct child of body
  const dropdownMenu = page.locator('body > div[x-ref="menu"]:visible');
  await expect(dropdownMenu).toBeVisible({ timeout: 5000 });

  // Verify all settings options are now in the dropdown
  await expect(dropdownMenu.locator('[data-testid="user-settings-button"]')).toBeVisible();
  await expect(dropdownMenu.locator('[data-testid="app-settings-button"]')).toBeVisible();
  await expect(dropdownMenu.locator('[data-testid="subscription-settings-button"]')).toBeVisible();
  await expect(dropdownMenu.locator('[data-testid="api-access-settings-button"]')).toBeVisible();

  // Verify help options are still there
  await expect(dropdownMenu.locator('[data-testid="changelog-button"]')).toBeVisible();
  await expect(dropdownMenu.locator('[data-testid="give-feedback-button"]')).toBeVisible();

  // Test that clicking User Settings navigates correctly
  const userSettingsOption = dropdownMenu.locator('[data-testid="user-settings-button"]');
  await userSettingsOption.click();

  // Wait for navigation to user settings page
  await page.waitForURL('/app/settings/user');
  await expect(page.locator('h1:has-text("User Settings")')).toBeVisible();
});