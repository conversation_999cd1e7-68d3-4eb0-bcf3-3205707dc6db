import { test, expect } from "./teamFixture";
import { generateRandomName, TestDataGenerator } from "../utils";

test.use({
  teamConfig: 20, // Use 5 teams by default for tests
});

test.describe("with no teams", () => {
  test.use({
    teamConfig: 0, // Start with no teams for the "can add a new team" test
  });

  test("user can add a new team successfully", async ({
    teamFixture,
    isolatedUser,
  }) => {
    // Navigate to the teams page and wait for any response
    const response = await isolatedUser.page.goto("/app/teams");
    console.log("Response status:", response?.status());

    // Check for any console errors
    isolatedUser.page.on("console", (msg) => {
      if (msg.type() === "error") {
        console.log("Console error:", msg.text());
      }
    });

    // Wait for page to fully load (cookie consent should already be handled by isolatedUser fixture)
    await isolatedUser.page.waitForLoadState("networkidle");

    // Locate and click the "Add Your First Team" button by id (shown when no teams exist)
    const addNewTeamButton = isolatedUser.page.locator("#add-first-team-btn");
    await addNewTeamButton.click();

    // Fill out the form fields
    const modal = isolatedUser.page.locator("#teamsNewForm");
    const teamData = TestDataGenerator.team();

    await modal.locator('input[name="name"]').fill(teamData.name);
    await modal.locator("#description").fill(teamData.description);

    // Submit the form
    await modal.locator('button[type="submit"]').click();

    // Wait for the modal to close (form submission triggers HTMX events)
    await expect(modal).not.toBeVisible();

    // Wait for HTMX teamsUpdated event to trigger table refresh
    // The PostNewTeam handler fires HXEventTeamsUpdated which should refresh the teams table
    await isolatedUser.page.waitForTimeout(1000);

    // Verify the new team appears in the table first
    const teamRow = isolatedUser.page.locator("table tbody tr").first();
    await expect(teamRow).toBeVisible({ timeout: 5000 });

    // Verify team details directly in the table row (without using search)
    const nameInput = teamRow.locator("td").nth(0).locator("input");
    await expect(nameInput).toHaveValue(teamData.name);

    const descriptionInput = teamRow.locator("td").nth(1).locator("input");
    await expect(descriptionInput).toHaveValue(teamData.description);
  });
});

test("user can delete a team", async ({ teamFixture, page }) => {
  // Navigate to teams page
  await page.goto("/app/teams");

  // Wait for teams to load
  await page.waitForSelector("table tbody tr", { timeout: 10000 });

  // Get initial count of teams
  const initialRows = await page.locator("table tbody tr").count();
  expect(initialRows).toBeGreaterThan(0);

  // Get the first team row
  const firstRow = page.locator("table tbody tr").first();

  // Set up dialog handler before clicking
  page.on("dialog", async (dialog) => {
    expect(dialog.type()).toBe("confirm");
    expect(dialog.message()).toContain(
      "Are you sure you want to delete",
    );
    expect(dialog.message()).toContain(
      "This action cannot be undone.",
    );
    await dialog.accept();
  });

  // Get the team ID to be more specific with our selectors
  const teamId = await firstRow.getAttribute("id");
  const teamIdNumber = teamId?.replace("team-row-", "") || "";

  // Click the dropdown button in the actions column
  const actionsDropdown = firstRow.locator(
    `[data-testid="team-${teamIdNumber}-actions-dropdown"]`,
  );
  await actionsDropdown.click();

  // Wait for dropdown to open
  await page.waitForTimeout(500);

  // Click the delete option in the dropdown (now specific to this team)
  const deleteOption = page.locator(
    `[data-testid="team-${teamIdNumber}-delete"]`,
  );
  await deleteOption.click();

  // Wait for the row to be removed
  await page.waitForTimeout(1000);

  // Verify team is removed from the table
  const remainingRows = await page.locator("table tbody tr").count();
  expect(remainingRows).toBe(initialRows - 1);
});

test("user can update a team name", async ({ teamFixture, page }) => {
  // Navigate to teams page
  await page.goto("/app/teams");

  // Wait for teams to load
  await page.waitForSelector("table tbody tr", { timeout: 10000 });

  // Get the first team row
  const firstRow = page.locator("table tbody tr").first();

  // Get the team ID from the row to track it specifically
  const teamId = await firstRow.getAttribute("id");
  const teamIdNumber = teamId?.replace("team-row-", "") || "";

  // Get the current team name from the input field
  const nameInput = firstRow.locator("td").nth(0).locator("input");
  const originalName = await nameInput.inputValue();

  // Generate a new name
  const newName = TestDataGenerator.string("Updated", 10);

  // Clear the input and type the new name
  await nameInput.clear();
  await nameInput.fill(newName);

  // Wait for the HTMX inline update request
  const responsePromise = page.waitForResponse(
    (response) =>
      response.url().includes("/teams/") &&
      response.url().includes("/inline") &&
      response.request().method() === "PUT",
  );

  // Trigger the update by pressing Enter or clicking outside
  await nameInput.press("Enter");

  // Wait for the HTMX PUT request to complete and verify it succeeded
  const response = await responsePromise;
  expect(response.status()).toBe(200);

  // Verify the input still contains the updated value immediately after the update
  await expect(nameInput).toHaveValue(newName);

  // Refresh the page to verify the change persisted
  await page.reload();
  await page.waitForSelector("table tbody tr", { timeout: 10000 });

  // Look for the specific team row after reload
  const reloadedTeamRow = page.locator(`#team-row-${teamIdNumber}`);

  // If the specific row exists, check its name
  if ((await reloadedTeamRow.count()) > 0) {
    const reloadedNameInput = reloadedTeamRow
      .locator("td")
      .nth(0)
      .locator("input");
    await expect(reloadedNameInput).toHaveValue(newName);
  } else {
    // Row might be on a different page due to sorting, search for it
    const searchInput = page.locator('[data-testid="teams-search-bar"]');
    await searchInput.fill(newName);
    await page.waitForTimeout(1000);

    // Should find exactly one result
    const searchResults = page.locator("table tbody tr");
    await expect(searchResults).toHaveCount(1);

    const foundNameInput = searchResults
      .first()
      .locator("td")
      .nth(0)
      .locator("input");
    await expect(foundNameInput).toHaveValue(newName);
  }
});

test("user can update a team description", async ({ teamFixture, page }) => {
  // Navigate to teams page
  await page.goto("/app/teams");

  // Wait for teams to load
  await page.waitForSelector("table tbody tr", { timeout: 10000 });

  // Get the first team row
  const firstRow = page.locator("table tbody tr").first();

  // Get the team ID from the row to track it specifically
  const teamId = await firstRow.getAttribute("id");
  const teamIdNumber = teamId?.replace("team-row-", "") || "";

  // Get the current team description from the input field (second column)
  const descriptionInput = firstRow.locator("td").nth(1).locator("input");
  const originalDescription = await descriptionInput.inputValue();

  // Generate a new description
  const newDescription = `Updated description ${TestDataGenerator.string("", 10)}`;

  // Clear the input and type the new description
  await descriptionInput.clear();
  await descriptionInput.fill(newDescription);

  // Trigger the update by pressing Enter
  await descriptionInput.press("Enter");

  // Wait for the HTMX delay (900ms) plus processing time for the teams table refresh
  await page.waitForTimeout(3000);

  // Get a fresh reference to the specific team row using its ID
  const updatedTeamRow = page.locator(`#team-row-${teamIdNumber}`);
  const updatedDescriptionInput = updatedTeamRow
    .locator("td")
    .nth(1)
    .locator("input");

  // Verify the description has been updated
  await expect(updatedDescriptionInput).toHaveValue(newDescription);

  // Refresh the page to verify the change persisted
  await page.reload();
  await page.waitForSelector("table tbody tr", { timeout: 10000 });

  // Verify the updated description is still there after refresh using the specific team ID
  const refreshedTeamRow = page.locator(`#team-row-${teamIdNumber}`);
  const refreshedDescriptionInput = refreshedTeamRow
    .locator("td")
    .nth(1)
    .locator("input");
  await expect(refreshedDescriptionInput).toHaveValue(newDescription);
});

test("user can filter teams using search bar", async ({
  teamFixture,
  page,
}) => {
  // Navigate to teams page
  await page.goto("/app/teams");

  // Wait for teams to load
  await page.waitForSelector("table tbody tr", { timeout: 10000 });

  // Get all team rows to ensure we have multiple teams
  const allRows = await page.locator("table tbody tr").count();
  expect(allRows).toBeGreaterThan(1); // Should have multiple teams to test filtering

  // Pick a random team row (not the first one to make it more interesting)
  const randomRowIndex = TestDataGenerator.number(Math.min(allRows, 3)); // Pick from first 3 teams
  const randomRow = page.locator("table tbody tr").nth(randomRowIndex);

  // Get the team name from the random row
  const nameInput = randomRow.locator("td").nth(0).locator("input");
  const teamName = await nameInput.inputValue();

  // Use the search bar to filter by this team name
  const searchInput = page.locator('[data-testid="teams-search-bar"]');
  await expect(searchInput).toBeVisible();
  await searchInput.clear();
  await searchInput.fill(teamName);

  // Wait for HTMX to process the search
  await page.waitForTimeout(1000);

  // Verify that the filtered results show only the searched team
  const filteredRows = await page.locator("table tbody tr").count();
  expect(filteredRows).toBe(1);

  // Verify the filtered team is the one we searched for
  const filteredRow = page.locator("table tbody tr").first();
  const filteredNameInput = filteredRow.locator("td").nth(0).locator("input");
  await expect(filteredNameInput).toHaveValue(teamName);

  // Test partial search - use first 3 characters of team name
  const partialSearch = teamName.substring(0, 3);
  await searchInput.clear();
  await searchInput.fill(partialSearch);

  // Wait for HTMX to process the partial search
  await page.waitForTimeout(1000);

  // Verify that partial search returns results (should be at least 1)
  const partialResults = await page.locator("table tbody tr").count();
  expect(partialResults).toBeGreaterThanOrEqual(1);

  // Verify that at least one result contains our original team
  const partialRows = page.locator("table tbody tr");
  let foundOriginalTeam = false;
  for (let i = 0; i < (await partialRows.count()); i++) {
    const rowNameInput = partialRows
      .nth(i)
      .locator("td")
      .nth(0)
      .locator("input");
    const rowName = await rowNameInput.inputValue();
    if (rowName === teamName) {
      foundOriginalTeam = true;
      break;
    }
  }
  expect(foundOriginalTeam).toBe(true);

  // Clear search to show all teams again
  await searchInput.clear();

  // Wait for HTMX to process the cleared search
  await page.waitForTimeout(1000);

  // Verify all teams are shown again
  const clearedResults = await page.locator("table tbody tr").count();
  expect(clearedResults).toBe(allRows);
});

test("user can print teams and popup window opens with content", async ({
  teamFixture,
  page,
}) => {
  // Navigate to teams page
  await page.goto("/app/teams");

  // Wait for teams to load
  await page.waitForSelector("table tbody tr", { timeout: 10000 });

  // Find and click the teams actions dropdown button
  const actionsDropdown = page.locator(
    '[data-testid="teams-actions-dropdown"]',
  );
  await actionsDropdown.click();

  // Set up popup listener before clicking print
  const popupPromise = page.waitForEvent("popup");

  // Click the print button
  const printButton = page.locator('[data-testid="print-teams-button"]');
  await printButton.click();

  // Wait for the popup window to open
  const popup = await popupPromise;

  // Wait for content to be written to the popup
  await popup.waitForLoadState("networkidle");
  await popup.waitForTimeout(1000);

  // Get the popup content
  const popupContent = await popup.content();

  // Verify the popup contains proper HTML structure
  expect(popupContent).toContain("<!DOCTYPE html>");
  expect(popupContent).toContain("<html");
  expect(popupContent).toContain("<body>");

  // Check that the popup contains the teams title
  expect(popupContent).toContain("Teams Report");
  expect(popupContent).toContain("Teams");

  // Check that the popup contains teams table structure
  expect(popupContent).toContain('<table class="print-table">');
  expect(popupContent).toContain("<th>Team Name</th>");
  expect(popupContent).toContain("<th>Description</th>");
  expect(popupContent).toContain("<th>Status</th>");

  // Check that print-specific CSS is included
  expect(popupContent).toContain("@media print");

  // Close the popup
  await popup.close();
});

test("print styles are applied correctly with enhanced CSS", async ({
  teamFixture,
  page,
}) => {
  // Navigate to teams page
  await page.goto("/app/teams");

  // Wait for teams to load
  await page.waitForSelector("table tbody tr", { timeout: 10000 });

  // Find and click the teams actions dropdown button
  const actionsDropdown = page.locator(
    '[data-testid="teams-actions-dropdown"]',
  );
  await actionsDropdown.click();

  // Set up popup listener before clicking print
  const popupPromise = page.waitForEvent("popup");

  // Click the print button
  const printButton = page.locator('[data-testid="print-teams-button"]');
  await printButton.click();

  // Wait for the popup window to open
  const popup = await popupPromise;
  await popup.waitForLoadState();

  // Get the content of the popup window
  const popupContent = await popup.content();

  // Verify enhanced print CSS classes are present
  expect(popupContent).toContain('class="print-header"');
  expect(popupContent).toContain('class="print-title"');
  expect(popupContent).toContain('class="print-subtitle"');
  expect(popupContent).toContain('class="print-table"');
  expect(popupContent).toContain('class="print-footer"');

  // Verify enhanced print CSS styles are included
  expect(popupContent).toContain("@media print");
  expect(popupContent).toContain("@page");
  expect(popupContent).toContain("page-break-inside: avoid");
  expect(popupContent).toContain("print-color-adjust: exact");
  expect(popupContent).toContain("table-header-group");

  // Verify no-print class handling
  expect(popupContent).toContain(".no-print");
  expect(popupContent).toContain("display: none !important");

  // Verify print-specific table styling
  expect(popupContent).toContain(".print-table th, .print-table td");
  expect(popupContent).toContain("border-collapse: collapse");

  // Close the popup
  await popup.close();
});
