// Import our playerFixture since seasons need players
import { test as playerTest, PlayerFixture } from './playerFixture';

// Define interfaces for type safety
interface SeasonConfig {
  count: number;
  seasonType: string;
  frequency: string;
  amountOfTables: number;
  startDateOffset: number;
}

interface Player {
  id: string;
  // Add other relevant fields if needed
}

interface Season {
  id: string;
  name: string;
  matches: any[];
  // Add other relevant fields if needed
}

export interface SeasonFixture {
  seasons: Season[];
  page: any; // Adjust type based on <PERSON><PERSON>'s page type if needed
  players: Player[];
}

// Helper functions to generate random data
import { TestDataGenerator, generateRandomSeasonName } from "../testData";

// Default configuration
export const defaultSeasonConfig: SeasonConfig = {
  count: 2,                // Number of seasons to create
  seasonType: 'other',     // Type of season (pool, bowling, other)
  frequency: 'weekly',     // Frequency (weekly, biweekly, monthly, quarterly, yearly)
  amountOfTables: 2,       // Number of tables/matches that can be played simultaneously
  startDateOffset: 0       // Days from today to start the season
};

// Interface for custom match columns
interface CustomMatchColumn {
  name?: string;
  fieldType?: string;
  description?: string;
  isRequired?: boolean;
  displayOrder?: number;
}

// Extend the player test to add seasonFixture
export const test = playerTest.extend<{
  seasonConfig: SeasonConfig;
  seasonFixture: SeasonFixture;
  playerFixture: PlayerFixture;
  customMatchColumns: CustomMatchColumn[];
}>({
  // Accept seasonConfig as an object with default values
  seasonConfig: [defaultSeasonConfig, { option: true }],
  
  // Accept customMatchColumns as an array with default empty value
  customMatchColumns: [[], { option: true }],

  // Create the season fixture
  seasonFixture: async ({ request, playerFixture, seasonConfig, customMatchColumns, isolatedUser}, use) => {
    // We need players from the playerFixture
    if (!playerFixture.players || playerFixture.players.length < 2) {
      throw new Error('Season fixture requires at least 2 players in playerFixture');
    }

    const today = new Date();
    const playerIds = playerFixture.players.map((player) => player.id);

    // Prepare all season data
    const seasonDataList = Array.from({ length: seasonConfig.count }, (_, i) => {
      const startDate = new Date(today);
      startDate.setDate(startDate.getDate() + seasonConfig.startDateOffset);
      return {
        name: generateRandomSeasonName(),
        seasontype: seasonConfig.seasonType,
        frequency: seasonConfig.frequency,
        startDate: startDate.toISOString().split('T')[0],
        playerIds: playerIds,
        amountOfTables: seasonConfig.amountOfTables,
        userId: isolatedUser.userId,
      };
    });

    // Create all seasons in parallel
    const seasonResponses = await Promise.all(
      seasonDataList.map((seasonData) =>
        request.post('/dev/seasons', { data: seasonData })
      )
    );

    const createdSeasons: Season[] = [];
    for (let i = 0; i < seasonResponses.length; i++) {
      const response = seasonResponses[i];
      if (!response.ok()) {
        console.log(`Season creation failed: ${await response.text()}`);
        throw new Error('Failed to create season');
      }
      const season: Season = await response.json();
      // Get all matches for this season
      const matchesResponse = await request.get(`/dev/seasons/${season.id}/matches`);
      if (matchesResponse.ok()) {
        season.matches = await matchesResponse.json();
      } else {
        console.log(`Failed to retrieve matches for season ${season.id}`);
        season.matches = [];
      }
      createdSeasons.push(season);
    }

    // Pass the created seasons to the test
    await use({ 
      seasons: createdSeasons, 
      page: isolatedUser.page, 
      players: playerFixture.players.map(p => ({ ...p, id: String(p.id) })) 
    });

    // Clean up the fixture: delete all created seasons in parallel
    await Promise.all(
      createdSeasons.map((season) => request.delete(`/dev/seasons/${season.id}`))
    );
  },
});

export const expect = playerTest.expect;