// Import our authenticated test instead of the base test
import { test as base } from '../auth.setup';
import { TestDataGenerator } from "../testData";

// Define interfaces for type safety
interface PlayerData {
  name: string;
  email: string;
  preferredMatchGroup: number;
  emailNotificationsEnabled: boolean;
  userId: number;
}

interface Player {
  id: number;
  userId: number;
  teamId?: number;
  name: string;
  email?: string;
  pictureUrl?: string;
  createdAt: string;
  updatedAt: string;
  preferredMatchGroup: number;
  isActive: boolean;
  emailNotificationsEnabled: boolean;
  emailReminderPreferences: string;
}

interface TeamData {
  name: string;
  description: string;
  userId: number;
}

interface Team {
  id: number;
  userId: number;
  name: string;
  description?: string;
  pictureUrl?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

interface SeasonData {
  name: string;
  description: string;
  userId: number;
  startDate: string;
  seasonType: string;
  frequency: string;
}

interface Season {
  id: number;
  userId: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

interface MatchData {
  userId: number;
  seasonId: number;
  title: string;
  description?: string;
  date: string;
}

interface Match {
  id: number;
  userId: number;
  seasonId: number;
  title: string;
  description?: string;
  date: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

export interface LimitFixture {
  players: Player[];
  teams: Team[];
  seasons: Season[];
  matches: Match[];
  page: any;
}

// Configuration interface for the limit fixture
interface LimitConfig {
  playerCount?: number;
  teamCount?: number;
  seasonCount?: number;
  matchCount?: number;
}

// Helper functions to generate random data
function generateRandomName(): string {
  return TestDataGenerator.player().name;
}

function generateRandomEmail(): string {
  return TestDataGenerator.player().email;
}

function generateRandomTeamName(): string {
  return TestDataGenerator.team().name;
}

function generateRandomDescription(): string {
  return TestDataGenerator.team().description;
}

function generateRandomSeasonName(): string {
  return TestDataGenerator.season().name;
}

// Extend base test by providing "limitFixture"
export const test = base.extend<{
  playerConfig: LimitConfig;
  teamConfig: LimitConfig;  
  seasonConfig: LimitConfig;
  matchConfig: LimitConfig;
  limitFixture: LimitFixture;
}>({
  // Accept configs with default values
  playerConfig: [{ playerCount: 0 }, { option: true }],
  teamConfig: [{ teamCount: 0 }, { option: true }],
  seasonConfig: [{ seasonCount: 0 }, { option: true }],
  matchConfig: [{ matchCount: 0 }, { option: true }],

  limitFixture: async ({ request, playerConfig, teamConfig, seasonConfig, matchConfig, isolatedUser }, use) => {
    const createdPlayers: Player[] = [];
    const createdTeams: Team[] = [];
    const createdSeasons: Season[] = [];
    const createdMatches: Match[] = [];

    try {
      // Create teams first
      const teamCount = teamConfig.teamCount || 0;
      for (let i = 0; i < teamCount; i++) {
        const teamData: TeamData = {
          name: generateRandomTeamName(),
          description: generateRandomDescription(),
          userId: isolatedUser.userId
        };

        const response = await request.post('/dev/teams', { data: teamData });

        if (!response.ok()) {
          console.log(`Team creation failed: ${await response.text()}`);
          throw new Error('Failed to create team');
        }

        const team: Team = await response.json();
        createdTeams.push(team);
      }

      // Create players
      const playerCount = playerConfig.playerCount || 0;
      for (let i = 0; i < playerCount; i++) {
        const playerData: PlayerData = {
          name: generateRandomName(),
          email: generateRandomEmail(),
          preferredMatchGroup: i + 1,
          emailNotificationsEnabled: i % 2 === 0,
          userId: isolatedUser.userId
        };

        const response = await request.post('/dev/players', { data: playerData });

        if (!response.ok()) {
          console.log(`Player creation failed: ${await response.text()}`);
          throw new Error('Failed to create player');
        }

        const player: Player = await response.json();
        createdPlayers.push(player);
      }

      // Create seasons
      const seasonCount = seasonConfig.seasonCount || 0;
      for (let i = 0; i < seasonCount; i++) {
        const seasonData: SeasonData = {
          name: generateRandomSeasonName(),
          description: generateRandomDescription(),
          userId: isolatedUser.userId,
          startDate: new Date(Date.now() + i * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Weekly intervals
          seasonType: 'pool',
          frequency: 'weekly'
        };

        const response = await request.post('/dev/seasons', { data: seasonData });

        if (!response.ok()) {
          console.log(`Season creation failed: ${await response.text()}`);
          throw new Error('Failed to create season');
        }

        const season: Season = await response.json();
        createdSeasons.push(season);
      }

      // Create matches if needed
      const matchCount = matchConfig.matchCount || 0;
      if (matchCount > 0 && createdSeasons.length > 0) {
        for (let i = 0; i < matchCount; i++) {
          const seasonId = createdSeasons[i % createdSeasons.length].id;
          const matchData: MatchData = {
            userId: isolatedUser.userId,
            seasonId: seasonId,
            title: `Match ${i + 1}`,
            description: `Test match ${i + 1}`,
            date: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString()
          };

          const response = await request.post('/dev/matches', { data: matchData });

          if (!response.ok()) {
            console.log(`Match creation failed: ${await response.text()}`);
            throw new Error('Failed to create match');
          }

          const match: Match = await response.json();
          createdMatches.push(match);
        }
      }

      // Pass the created data to the test
      await use({ 
        players: createdPlayers, 
        teams: createdTeams, 
        seasons: createdSeasons,
        matches: createdMatches,
        page: isolatedUser.page 
      });

    } finally {
      // Clean up all created resources
      for (const match of createdMatches) {
        await request.delete(`/dev/matches/${match.id}`);
      }
      for (const season of createdSeasons) {
        await request.delete(`/dev/seasons/${season.id}`);
      }
      for (const player of createdPlayers) {
        await request.delete(`/dev/players/${player.id}`);
      }
      for (const team of createdTeams) {
        await request.delete(`/dev/teams/${team.id}`);
      }
    }
  },
});

export const expect = base.expect;
