import { test, expect } from "./limitFixture";

test.describe("Subscription Limits", () => {
  test.describe("Banner Threshold Testing", () => {
    test.describe("Players at 60% usage", () => {
      test.use({
        playerConfig: { playerCount: 12 } // 12/20 = 60%
      });

      test("shows 60% usage banner for players on settings page", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        // Look for limit banner with specific data-testid
        const limitBanner = page.locator('[data-testid="limit-banner-players"]');
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain("12");
        expect(bannerText).toContain("20");
        expect(bannerText).toContain("players");
      });
    });

    test.describe("Players at 80% usage", () => {
      test.use({
        playerConfig: { playerCount: 16 } // 16/20 = 80%
      });

      test("shows 80% usage banner for players on settings page", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator('[data-testid="limit-banner-players"]');
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain("16");
        expect(bannerText).toContain("20");
        expect(bannerText).toContain("players");
      });
    });

    test.describe("Players at 100% usage", () => {
      test.use({
        playerConfig: { playerCount: 20 } // 20/20 = 100%
      });

      test("shows 100% usage banner for players on settings page", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator('[data-testid="limit-banner-players"]');
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain("20");
        expect(bannerText).toContain("20");
        expect(bannerText).toContain("players");
      });
    });

    test.describe("Teams at 80% usage", () => {
      test.use({
        teamConfig: { teamCount: 4 } // 4/5 = 80%
      });

      test("shows 80% usage banner for teams on settings page", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator('[data-testid="limit-banner-teams"]');
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain("4");
        expect(bannerText).toContain("5");
        expect(bannerText).toContain("teams");
      });
    });

    test.describe("Teams at 100% usage", () => {
      test.use({
        teamConfig: { teamCount: 5 } // 5/5 = 100%
      });

      test("shows 100% usage banner for teams on settings page", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator('[data-testid="limit-banner-teams"]');
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain("5");
        expect(bannerText).toContain("5");
        expect(bannerText).toContain("teams");
      });
    });

    test.describe("Seasons at 60% usage", () => {
      test.use({
        seasonConfig: { seasonCount: 2 }, // 2/3 = ~67% (closest to 60%)
        playerConfig: { playerCount: 8 }
      });

      test("shows 60% usage banner for seasons on settings page", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator('[data-testid="limit-banner-seasons"]');
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain("2");
        expect(bannerText).toContain("3");
        expect(bannerText).toContain("seasons");
      });
    });

    test.describe("Seasons at 100% usage", () => {
      test.use({
        seasonConfig: { seasonCount: 3 }, // 3/3 = 100%
        playerConfig: { playerCount: 8 }
      });

      test("shows 100% usage banner for seasons on settings page", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        const limitBanner = page.locator('[data-testid="limit-banner-seasons"]');
        await expect(limitBanner).toBeVisible();

        const bannerText = await limitBanner.textContent();
        expect(bannerText).toContain("3");
        expect(bannerText).toContain("3");
        expect(bannerText).toContain("seasons");
      });
    });
  });

  test.describe("Limit Enforcement", () => {
    test.describe("Player limit at 20/20", () => {
      test.use({
        playerConfig: { playerCount: 20 }
      });

      test("prevents creating 21st player when at 20/20 limit", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        // Go to players page and click add new player button
        await page.goto("/app/players");
        await page.waitForLoadState("networkidle");

        // Click the add new player button to open modal
        await page.click('#add-new-player-btn');
        await page.waitForLoadState("networkidle");

        // Fill the form in the modal
        const modal = page.locator('#playersNewForm');
        await modal.locator('input[name="name"]').fill("Player 21");
        await modal.locator('input[name="email"]').fill("<EMAIL>");
        await modal.locator('button[type="submit"]').click();

        // Should see error toast
        const toast = page.locator('#toast-body-container');
        await expect(toast).toBeVisible();
        const toastText = await toast.textContent();
        expect(toastText?.toLowerCase()).toContain("limit");
      });
    });

    test.describe("Season limit at 3/3", () => {
      test.use({
        seasonConfig: { seasonCount: 3 },
        playerConfig: { playerCount: 8 }
      });

      test("prevents creating 4th season when at 3/3 limit", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/seasons/new");
        await page.waitForLoadState("networkidle");

        // Fill the multi-step form
        await page.fill('input[name="name"]', "Season 4");

        // Navigate through the form steps and submit
        await page.click('button:has-text("Next")');
        await page.waitForTimeout(500);

        // Select some players (step 2)
        const playerCheckboxes = page.locator('input[type="checkbox"][name="playerIds[]"]');
        const checkboxCount = await playerCheckboxes.count();
        if (checkboxCount >= 2) {
          await playerCheckboxes.nth(0).check();
          await playerCheckboxes.nth(1).check();
        }

        await page.click('button:has-text("Next")');
        await page.waitForTimeout(500);

        // Fill remaining fields and submit
        await page.fill('input[name="amountOfTables"]', "1");
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000); // Wait for form submission

        // Check if we're still on the form page (indicating submission failed due to limit)
        // OR if we were redirected to an error page
        const currentUrl = page.url();
        const isStillOnForm = currentUrl.includes("/seasons/new");
        const isOnErrorPage = currentUrl.includes("/error") || currentUrl.includes("/app-error");

        // Either we should still be on the form page OR on an error page
        const submissionBlocked = isStillOnForm || isOnErrorPage;
        expect(submissionBlocked).toBe(true);
      });
    });

    test.describe("Team limit at 5/5", () => {
      test.use({
        teamConfig: { teamCount: 5 }
      });

      test("prevents creating 6th team when at 5/5 limit", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        // Go to teams page and click add new team button
        await page.goto("/app/teams");
        await page.waitForLoadState("networkidle");

        // Click the add new team button to open modal
        await page.click('#add-new-team-btn');
        await page.waitForLoadState("networkidle");

        // Fill the form in the modal
        const modal = page.locator('#teamsNewForm');
        await modal.locator('input[name="name"]').fill("Team 6");

        // Submit the form and wait for response
        await modal.locator('button[type="submit"]').click();
        await page.waitForTimeout(2000); // Wait for HTMX response

        // Check if the modal is still open (indicating form submission failed)
        const modalStillOpen = await modal.isVisible();
        expect(modalStillOpen).toBe(true);

        // Check for any error indication in the modal or page
        const errorElements = page.locator('.bg-red-100, .text-red-700, [data-testid*="error"], .error');
        const hasError = await errorElements.count() > 0;
        expect(hasError).toBe(true);

        // Alternative: Check if the team was NOT created by checking the teams count
        await page.goto("/app/teams");
        await page.waitForLoadState("networkidle");

        // Count team rows - should still be 5
        const teamRows = page.locator('[data-testid^="team-row-"], .team-row, tbody tr');
        const teamCount = await teamRows.count();
        expect(teamCount).toBe(5); // Should still be 5, not 6
      });
    });

    test.describe("Player import over capacity", () => {
      test.use({
        playerConfig: { playerCount: 19 } // 19/20, room for exactly 1 more
      });

      test("prevents player import when it would exceed player limit", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        await page.goto("/app/players/import");
        await page.waitForLoadState("networkidle");

        // Try to import 5 players (should fail - would exceed limit)
        const csvData = `Name,Email
Player 20,<EMAIL>
Player 21,<EMAIL>
Player 22,<EMAIL>
Player 23,<EMAIL>
Player 24,<EMAIL>`;

        const textarea = page.locator('textarea[name="csvData"], #csvData, textarea');
        if (await textarea.count() > 0) {
          await textarea.fill(csvData);
          await page.click('button[type="submit"]');

          // Should see error toast about exceeding limit
          const toast = page.locator('#toast-body-container');
          await expect(toast).toBeVisible();
          const toastText = await toast.textContent();
          expect(toastText?.toLowerCase()).toContain("limit");
        }
      });
    });
  });

  test.describe("Multiple Resource Limits", () => {
    test.describe("Multiple resources near limits", () => {
      test.use({
        playerConfig: { playerCount: 19 }, // 19/20 = 95%
        seasonConfig: { seasonCount: 3 },   // 3/3 = 100%
        teamConfig: { teamCount: 4 }      // 4/5 = 80%
      });

      test("shows multiple limit banners when near limits on multiple resources", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        // Test on settings page where all limit banners are shown
        await page.goto("/app/settings/app");
        await page.waitForLoadState("networkidle");

        // Check for players limit banner
        const playersLimitBanner = page.locator('[data-testid="limit-banner-players"]');
        await expect(playersLimitBanner).toBeVisible();

        // Check for seasons limit banner
        const seasonsLimitBanner = page.locator('[data-testid="limit-banner-seasons"]');
        await expect(seasonsLimitBanner).toBeVisible();

        // Check for teams limit banner
        const teamsLimitBanner = page.locator('[data-testid="limit-banner-teams"]');
        await expect(teamsLimitBanner).toBeVisible();
      });
    });

    test.describe("All resources at limit", () => {
      test.use({
        playerConfig: { playerCount: 20 }, // 20/20 = 100%
        seasonConfig: { seasonCount: 3 },   // 3/3 = 100%
        teamConfig: { teamCount: 5 }      // 5/5 = 100%
      });

      test("prevents resource creation when multiple resources are at limit", async ({
        limitFixture
      }) => {
        const { page } = limitFixture;

        // Try to create player - should fail (this one should show toast since players form targets toast container)
        await page.goto("/app/players");
        await page.waitForLoadState("networkidle");

        await page.click('#add-new-player-btn');
        await page.waitForLoadState("networkidle");

        const playerModal = page.locator('#playersNewForm');
        await playerModal.locator('input[name="name"]').fill("Player 21");
        await playerModal.locator('input[name="email"]').fill("<EMAIL>");
        await playerModal.locator('button[type="submit"]').click();
        await page.waitForTimeout(1000);

        // For players, check toast since it's configured to show toasts
        const toast = page.locator('#toast-body-container');
        await expect(toast).toBeVisible();
        const toastText = await toast.textContent();
        expect(toastText?.toLowerCase()).toContain("limit");

        // Try to create team - should fail (check modal stays open)
        await page.goto("/app/teams");
        await page.waitForLoadState("networkidle");

        await page.click('#add-new-team-btn');
        await page.waitForLoadState("networkidle");

        const teamModal = page.locator('#teamsNewForm');
        await teamModal.locator('input[name="name"]').fill("Team 6");
        await teamModal.locator('button[type="submit"]').click();
        await page.waitForTimeout(2000);

        // Check modal is still open (indicating failure)
        const teamModalStillOpen = await teamModal.isVisible();
        expect(teamModalStillOpen).toBe(true);

        // Try to create season - should fail (check we stay on form page)
        await page.goto("/app/seasons/new");
        await page.waitForLoadState("networkidle");

        await page.fill('input[name="name"]', "Season 4");
        await page.click('button:has-text("Next")');
        await page.waitForTimeout(500);

        // Select some players
        const playerCheckboxes = page.locator('input[type="checkbox"][name="playerIds[]"]');
        const checkboxCount = await playerCheckboxes.count();
        if (checkboxCount >= 2) {
          await playerCheckboxes.nth(0).check();
          await playerCheckboxes.nth(1).check();
        }

        await page.click('button:has-text("Next")');
        await page.waitForTimeout(500);

        await page.fill('input[name="amountOfTables"]', "1");
        await page.click('button[type="submit"]');
        await page.waitForTimeout(2000);

        // Check we're still on the form page (indicating failure)
        const currentUrl = page.url();
        expect(currentUrl).toContain("/seasons/new");
      });
    });
  });
});