import { config } from 'dotenv';
import * as path from 'path';

// Load environment variables from .env file
config({ path: path.resolve(__dirname, '../../.env') });

/**
 * Get the backend port from environment variable, matching the backend logic
 * For E2E tests, we expect the backend to be running on the development port (8000)
 * This should match the port in .env.development
 */
export function getBackendPort(): string {
  const port = process.env.COACHPAD_BACKEND_PORT || '8000';
  
  // E2E tests expect development environment
  if (port !== '8000') {
    console.warn(`Warning: Expected development port 8000, got ${port}. Using 8000 for E2E tests.`);
    return '8000';
  }
  
  return port;
}

/**
 * Get the base URL for the application
 */
export function getBaseURL(): string {
  return `http://localhost:${getBackendPort()}`;
}