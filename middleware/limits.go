package middleware

import (
	"fmt"
	"net/http"

	"github.com/j-em/coachpad/cookies"
	"github.com/j-em/coachpad/pkg/limits"
	"github.com/j-em/coachpad/templates/ui/toast"
	"github.com/labstack/echo/v4"
)

// LimitsConfig holds the configuration for the limits middleware
type LimitsConfig struct {
	LimitsService *limits.Service
}

// LimitsMiddleware creates middleware for checking resource limits
type LimitsMiddleware struct {
	limitsService *limits.Service
}

// NewLimitsMiddleware creates a new limits middleware instance
func NewLimitsMiddleware(limitsService *limits.Service) *LimitsMiddleware {
	return &LimitsMiddleware{
		limitsService: limitsService,
	}
}

// CheckResourceLimit returns middleware that checks if user can create a resource
func (m *LimitsMiddleware) CheckResourceLimit(resourceType limits.ResourceType) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userID := GetUserID(c)
			if userID == 0 {
				return echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
			}

			result, err := m.limitsService.CheckLimit(c.Request().Context(), userID, resourceType)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, "Failed to check limits")
			}

			if !result.Allowed {
				// Return toast error for HTMX requests
				if c.Request().Header.Get("HX-Request") == "true" {
					lang := cookies.GetLanguageFromCookie(c)
					return toast.Toast(toast.ToastConfig{
						ID:        "limit-exceeded-toast",
						Message:   result.ErrorMessage,
						Style:     "error",
						AutoClose: false,
						Lang:      lang,
					}).Render(c.Response().Writer)
				}

				// For regular requests, return JSON error
				return c.JSON(http.StatusPaymentRequired, map[string]interface{}{
					"error": map[string]interface{}{
						"code":          "LIMIT_EXCEEDED",
						"message":       result.ErrorMessage,
						"current_count": result.CurrentCount,
						"limit":         result.Limit,
						"resource_type": result.ResourceType,
					},
				})
			}

			// Store limit result in context for use in handlers
			c.Set("limitResult", result)
			return next(c)
		}
	}
}

// CheckBulkResourceLimit returns middleware that checks if user can create multiple resources
func (m *LimitsMiddleware) CheckBulkResourceLimit(resourceType limits.ResourceType, quantityField string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userID := GetUserID(c)
			if userID == 0 {
				return echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
			}

			// Extract quantity from form data
			quantityStr := c.FormValue(quantityField)
			quantity := 1 // Default to 1 if not specified

			if quantityStr != "" {
				if _, err := fmt.Sscanf(quantityStr, "%d", &quantity); err != nil {
					quantity = 1
				}
			}

			result, err := m.limitsService.CheckBulkLimit(c.Request().Context(), userID, resourceType, quantity)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, "Failed to check limits")
			}

			if !result.Allowed {
				// Return toast error for HTMX requests
				if c.Request().Header.Get("HX-Request") == "true" {
					lang := cookies.GetLanguageFromCookie(c)
					return toast.Toast(toast.ToastConfig{
						ID:        "bulk-limit-exceeded-toast",
						Message:   result.ErrorMessage,
						Style:     "error",
						AutoClose: false,
						Lang:      lang,
					}).Render(c.Response().Writer)
				}

				// For regular requests, return JSON error
				return c.JSON(http.StatusPaymentRequired, map[string]interface{}{
					"error": map[string]interface{}{
						"code":          "BULK_LIMIT_EXCEEDED",
						"message":       result.ErrorMessage,
						"current_count": result.CurrentCount,
						"limit":         result.Limit,
						"resource_type": result.ResourceType,
						"quantity":      quantity,
					},
				})
			}

			// Store limit result in context for use in handlers
			c.Set("limitResult", result)
			return next(c)
		}
	}
}

// RequireProTier returns middleware that requires a Pro subscription
func (m *LimitsMiddleware) RequireProTier() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userID := GetUserID(c)
			if userID == 0 {
				return echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
			}

			// Get current usage to check subscription tier
			usage, err := m.limitsService.GetCurrentUsage(c.Request().Context(), userID)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, "Failed to check subscription")
			}

			// Check if any resource has unlimited access (indicates Pro tier)
			isPro := false
			for _, result := range usage {
				if result.IsUnlimited {
					isPro = true
					break
				}
			}

			if !isPro {
				if c.Request().Header.Get("HX-Request") == "true" {
					lang := cookies.GetLanguageFromCookie(c)
					return toast.Toast(toast.ToastConfig{
						ID:        "pro-required-toast",
						Message:   "This feature requires a Pro subscription",
						Style:     "error",
						AutoClose: false,
						Lang:      lang,
					}).Render(c.Response().Writer)
				}

				return c.JSON(http.StatusPaymentRequired, map[string]interface{}{
					"error": map[string]interface{}{
						"code":    "PRO_REQUIRED",
						"message": "This feature requires a Pro subscription",
					},
				})
			}

			return next(c)
		}
	}
}
