import Alpine from "alpinejs";
import htmx from "htmx.org";
import "htmx-ext-head-support";
import "htmx-ext-sse";
import focus from "@alpinejs/focus";

// add the beginning of your app entry
import "vite/modulepreload-polyfill";

// Configure HTMX response handling
htmx.config = {
  ...htmx.config,
  responseHandling: [
    { code: "...", swap: true }, // catch all for any other response code
  ],
};

window.htmx = htmx;

import "./main.css";
import customColumnsTable from "./components/customColumnsTable";
import formValidationDirective from "./validation";
import datePickerComponent from "./datepicker";
import teleportDatePickerComponent from "./teleportDatepicker";
import selectComponent from "./selectComponent";
import dropdown from "./dropdown";
import cookieConsent from "./cookieConsent";
import { copyToClipboard } from "./utils.js";
import mobileMenuToggle from "./mobileMenuToggle.js";
import multiStepForm from "./multiStepForm.js";
import multiStepSignup from "./multiStepSignup.js";
import multiStepNewSeasonForm from "./multiStepNewSeasonForm.js";

// Make stripePayment globally available
import stripePayment from "./stripePayment.js";

Alpine.data("customColumnsTable", customColumnsTable);
Alpine.data("cookieConsent", cookieConsent);
Alpine.data("stripePayment", stripePayment);

// Register multi-step form and legacy signup component
Alpine.data("multiStepForm", multiStepForm);
Alpine.data("multiStepSignup", multiStepSignup);
Alpine.data("multiStepNewSeasonForm", multiStepNewSeasonForm);

// Register the form validation directive
Alpine.directive("validate-form", (el, directive, utilities) => {
  formValidationDirective(el, directive, utilities);
});

// Define a directive for the select component's x-data
Alpine.data("selectComponent", selectComponent);

// Define a directive for the datepicker component's x-data
Alpine.data("datePickerComponent", datePickerComponent);

// Define a directive for the teleport datepicker component's x-data
Alpine.data("teleportDatePickerComponent", teleportDatePickerComponent);

// Define a directive for the dropdown component's x-data
Alpine.data("dropdownComponent", dropdown);

// Add a global Alpine store for app-wide state
Alpine.store("appStore", {
  activeLink: "",
  init() {
    this.activeLink = window.location.pathname;
  },

  setActiveLink(link) {
    this.activeLink = link;
  },
  closeAllDropdowns() {
    console.log("Closing all dropdowns");
    window.dispatchEvent(new CustomEvent("close-all-dropdowns"));
  },
  // New function to close all modals throughout the application
  closeAllModals() {
    console.log("Closing all modals");
    window.dispatchEvent(new CustomEvent("close-all-modals"));
  },

  setActiveLinkAndClose(link) {
    this.activeLink = link;
    this.closeAllModals();
    this.closeAllDropdowns();
    // Close sidebar on mobile devices only after HTMX swap completes
    if (window.innerWidth < 768) {
      // md breakpoint in Tailwind
      // Listen for the next HTMX afterSwap event and then close the sidebar
      const closeSidebarAfterSwap = () => {
        window.dispatchEvent(new CustomEvent("close-mobile-sidebar"));
        document.removeEventListener("htmx:afterSwap", closeSidebarAfterSwap);
      };
      document.addEventListener("htmx:afterSwap", closeSidebarAfterSwap);
    }
  },

  closeAllMenus() {
    this.closeAllModals();
    this.closeAllDropdowns();
    // Close sidebar on mobile devices only after HTMX swap completes
    if (window.innerWidth < 768) {
      // md breakpoint in Tailwind
      // Listen for the next HTMX afterSwap event and then close the sidebar
      const closeSidebarAfterSwap = () => {
        window.dispatchEvent(new CustomEvent("close-mobile-sidebar"));
        document.removeEventListener("htmx:afterSwap", closeSidebarAfterSwap);
      };
      document.addEventListener("htmx:afterSwap", closeSidebarAfterSwap);
    }
  },
});

Alpine.plugin(focus);

// Add event listener for HTMX swaps to re-initialize Alpine.js for new content
document.addEventListener("htmx:afterSwap", function (event) {
  if (window.Alpine) {
    window.Alpine.initTree(event.detail.elt);
    // Always sync sidebar activeLink to current URL after any HTMX swap
    if (window.Alpine.store && window.Alpine.store("appStore")) {
      // Use a small delay to ensure the URL has been updated by the browser
      setTimeout(() => {
        window.Alpine.store("appStore").setActiveLink(window.location.pathname);
      }, 0);
    }
  }

  // Handle copy to clipboard trigger
  const detail = event.detail.xhr?.getResponseHeader("HX-Trigger-After-Swap");
  if (detail) {
    try {
      const triggers = JSON.parse(detail);
      if (triggers.copyToClipboard) {
        copyToClipboard(triggers.copyToClipboard);
      }
    } catch (err) {
      console.error("Error parsing HX-Trigger-After-Swap header:", err);
    }
  }
});

// Handle aborted HTMX requests to sync Alpine.js store back to current URL
document.addEventListener("htmx:sendAbort", function () {
  if (Alpine.store("appStore")) {
    const currentPath = window.location.pathname;
    const storeActiveLink = window.Alpine.store("appStore").activeLink;

    // When a request is aborted, the URL doesn't change but the store might have been updated
    // Sync store back to the current URL
    if (storeActiveLink !== currentPath) {
      window.Alpine.store("appStore").setActiveLink(currentPath);
    }
  }
});

// Handle HTMX request completion to ensure state synchronization
document.addEventListener("htmx:afterRequest", function (event) {
  if (window.Alpine && window.Alpine.store && window.Alpine.store("appStore")) {
    const currentPath = window.location.pathname;
    const storeActiveLink = window.Alpine.store("appStore").activeLink;

    // Always sync after any request completes, regardless of success/failure
    if (storeActiveLink !== currentPath) {
      window.Alpine.store("appStore").setActiveLink(currentPath);
    }
  }
});

// Handle HTMX response errors to ensure state synchronization
document.addEventListener("htmx:responseError", function (event) {
  if (window.Alpine && window.Alpine.store && window.Alpine.store("appStore")) {
    const currentPath = window.location.pathname;
    const storeActiveLink = window.Alpine.store("appStore").activeLink;

    // On response errors, ensure the active link matches the current URL
    if (storeActiveLink !== currentPath) {
      window.Alpine.store("appStore").setActiveLink(currentPath);
    }
  }
});

// Handle HTMX send errors to ensure state synchronization
document.addEventListener("htmx:sendError", function (event) {
  if (window.Alpine && window.Alpine.store && window.Alpine.store("appStore")) {
    const currentPath = window.location.pathname;
    const storeActiveLink = window.Alpine.store("appStore").activeLink;

    // On send errors, ensure the active link matches the current URL
    if (storeActiveLink !== currentPath) {
      window.Alpine.store("appStore").setActiveLink(currentPath);
    }
  }
});

// Handle browser back/forward button navigation to sync Alpine.js store
window.addEventListener("popstate", function () {
  if (Alpine.store("appStore")) {
    const currentPath = window.location.pathname;
    const storeActiveLink = window.Alpine.store("appStore").activeLink;

    // Only update if they don't match to avoid unnecessary reactivity triggers
    if (storeActiveLink !== currentPath) {
      window.Alpine.store("appStore").setActiveLink(currentPath);
    }
  }
});

Alpine.data("mobileMenuToggle", mobileMenuToggle);

// Expose functions to global scope for use in templates
window.copyToClipboard = copyToClipboard;

window.Alpine = Alpine;
window.Alpine.start();
