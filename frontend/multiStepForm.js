// Generic multi-step form Alpine.js directive
// Usage: multiStepForm(config)
// config = {
//   steps: [
//     { title: 'Step 1', fields: ['name', 'email'] },
//     { title: 'Step 2', fields: ['country'] },
//     ...
//   ],
//   fields: ['name', 'email', 'country', ...],
//   fieldMapping: { confirmPassword: 'confirm-password' },
//   validate: [fn1, fn2, ...], // per-step validation functions
//   focusSelectors: [fnOrSelector, ...],
//   locales: { step_of: 'Step {current} of {total}', ... },
//   onStepChange: (oldStep, newStep) => {},
//   submitOnEnter: true,
//   pollingInterval: 200,
//   enablePolling: true,
//   hooks: { afterFieldUpdate: (field, value) => {} },
// }
export default function multiStepForm(config = {}) {
  // Destructure config with defaults
  const {
    steps = [],
    fields = [],
    fieldMapping = {},
    initialData = {},
    validate = [],
    focusSelectors = [],
    locales: userLocales = {},
    submitOnEnter = true,
    pollingInterval = 200,
    enablePolling = true,
    onStepChange = () => {},
    hooks = {},
  } = config;

  const totalSteps = steps.length || 1;
  const locales = {
    step_of: 'Step {current} of {total}',
    ...userLocales,
  };

  // Single source of truth for form data
  const initialFormData = {};
  fields.forEach(f => { initialFormData[f] = ''; });
  Object.values(fieldMapping).forEach(f => { initialFormData[f] = ''; });
  // Apply initial data overrides
  Object.assign(initialFormData, initialData);

  return {
    currentStep: 1,
    totalSteps, // Expose totalSteps for Alpine template access
    formData: { ...initialFormData },
    stepValidation: {},
    locales,
    // Validation state
    errors: {},
    errorMessages: {},
    groupErrors: {},
    touchedFields: {}, // Track which fields have been interacted with
    stepErrorsEnabled: {}, // Track which steps should show errors

    init() {
      // Initialize step error visibility (start with step 1 enabled)
      for (let i = 1; i <= totalSteps; i++) {
        this.stepErrorsEnabled[i] = (i === 1); // Only step 1 starts with errors enabled
      }
      
      // Watch all formData changes for validation
      this.$watch(
        'formData',
        () => { this._validateAllSteps(); },
        { deep: true },
      );
      
      // Watch for changes to country and lang objects and extract their values
      this.$watch('formData.country', (country) => {
        if (country && typeof country === 'object' && country.value) {
          this.formData.country = country.value;
        }
      });
      
      this.$watch('formData.lang', (lang) => {
        if (lang && typeof lang === 'object' && lang.value) {
          this.formData.lang = lang.value;
        }
      });
      
      // Initialize form validation
      this._initValidation();
      // Set locales from template if available
      if (window.multiStepLocales) {
        this.locales = { ...this.locales, ...window.multiStepLocales };
      }
      this.$nextTick(() => {
        if (enablePolling) this._maybeStartPolling();
        this._focusFirst();
        if (submitOnEnter) this._setupEnter();
      });
    // Hook before HTMX requests to validate and sync data
    this.$el.addEventListener('htmx:beforeRequest', (e) => {
      // Sync form data before submission
      this.syncFormData();
      
      this._beforeRequestHandler(e);
    });
      // After HTMX swaps validation content, re-validate steps
      this.$el.addEventListener('htmx:afterSwap', (e) => {
        if (e.detail.target && e.detail.target.id === 'email-validation') {

          // Update emailValidationState based on server response
          const span = e.detail.target.querySelector('span');
          if (span && span.classList.contains('text-green-600')) {
            console.log("email is available");
            this.formData.emailValidationState = 'available';
          } else {
            this.formData.emailValidationState = 'unavailable';
          }
          this._validateAllSteps();
        }
      });
    },

    // --- Navigation ---
    nextStep() {
      // Enable errors for current step when trying to proceed
      this.stepErrorsEnabled[this.currentStep] = true;
      
      if (this._valid(this.currentStep)) {
        const old = this.currentStep;
        this.currentStep = Math.min(this.currentStep + 1, totalSteps);
        // Clear errors for the new step initially
        this._clearStepErrors(this.currentStep);
        onStepChange(old, this.currentStep);
        this.$nextTick(() => this._focusFirst());
      } else {
        // Force re-validation of current step fields to show errors
        this._validateCurrentStepFields();
      }
    },

    syncFormData() {
      // Create hidden inputs for form submission
      const form = this.$el;
      
      // Remove any existing sync inputs
      form.querySelectorAll('.sync-input').forEach(input => input.remove());
      
      // Create hidden inputs for each form data field
      Object.keys(this.formData).forEach(key => {
        const value = this.formData[key];
        
        if (key === 'playerIds' && Array.isArray(value)) {
          // Handle array fields specially
          value.forEach(playerId => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'playerIds[]';
            input.value = playerId;
            input.className = 'sync-input';
            form.appendChild(input);
          });
        } else if (value !== null && value !== undefined && value !== '') {
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = key;
          
          // Handle object values by extracting their 'value' property
          let finalValue = value;
          if (typeof value === 'object' && value !== null) {
            if (value.value !== undefined) {
              finalValue = value.value;
            } else {
              // If it's an object without a value property, try to JSON stringify it
              // This should not happen for select components, but is a fallback
              finalValue = JSON.stringify(value);
            }
          }
          
          input.value = finalValue;
          input.className = 'sync-input';
          form.appendChild(input);
        }
      });
    },
    prevStep() {
      const old = this.currentStep;
      this.currentStep = Math.max(this.currentStep - 1, 1);
      onStepChange(old, this.currentStep);
      this.$nextTick(() => this._focusFirst());
    },
    goToStep(n) {
      if (n <= this.currentStep || this.stepValidation[n]) {
        const old = this.currentStep;
        this.currentStep = n;
        onStepChange(old, this.currentStep);
        this.$nextTick(() => this._focusFirst());
      }
    },

    // --- Validation ---
    canProceedToNextStep() {
      return this._valid(this.currentStep);
    },
    
    // Check if field error should be shown (only if touched)
    shouldShowFieldError(fieldName) {
      return this.touchedFields[fieldName] && this.errors[fieldName];
    },
    
    // Find which step a field belongs to
    _getFieldStep(fieldName) {
      for (let i = 0; i < steps.length; i++) {
        if (steps[i].fields && steps[i].fields.includes(fieldName)) {
          return i + 1; // steps are 1-indexed
        }
      }
      return this.currentStep; // fallback to current step if not found
    },
    
    // Mark all fields in current step as touched
    _markCurrentStepFieldsAsTouched() {
      const currentStepFields = steps[this.currentStep - 1]?.fields || [];
      currentStepFields.forEach(fieldName => {
        this.touchedFields[fieldName] = true;
      });
    },
    
    // Force validation of current step fields
    _validateCurrentStepFields() {
      const currentStepFields = steps[this.currentStep - 1]?.fields || [];
      currentStepFields.forEach(fieldName => {
        const input = this.$el.querySelector(`[name="${fieldName}"]`) || this.$el.querySelector(`#${fieldName}`);
        if (input) {
          this.touchedFields[fieldName] = true;
          this._validateField(fieldName, input);
        }
      });
    },
    
    // Clear all errors for a specific step
    _clearStepErrors(stepNumber) {
      const stepFields = steps[stepNumber - 1]?.fields || [];
      stepFields.forEach(fieldName => {
        this.errors[fieldName] = false;
        this.errorMessages[fieldName] = '';
        this.touchedFields[fieldName] = false;
      });
      // Disable errors for this step
      this.stepErrorsEnabled[stepNumber] = false;
    },
    
    _valid(step) {
      const idx = step - 1;
      if (typeof validate[idx] === 'function') {
        return validate[idx](this.formData, this);
      }
      const flds = steps[idx]?.fields || [];
      return flds.every(f => (this.formData[f] || '').trim());
    },
    _validateAllSteps() {
      for (let i = 1; i <= totalSteps; i++) {
        this.stepValidation[i] = this._valid(i);
      }
    },

    // --- Enter key handling ---
    _setupEnter() {
      this.$el.addEventListener('keydown', e => {
        if (e.key === 'Enter' && !e.shiftKey && !this._isButton(e.target)) {
          e.preventDefault(); e.stopPropagation();
          if (this.currentStep === totalSteps) {
            this.$el.querySelector('button[type=submit]')?.click();
          } else {
            this.nextStep();
          }
        }
      });
    },
    _isButton(el) {
      return el.tagName === 'BUTTON' || el.type === 'submit' || !!el.closest('button');
    },

    // --- Focus ---
    _focusFirst() {
      const sel = focusSelectors[this.currentStep - 1];
      const el = sel
        ? (typeof sel === 'function' ? sel(this) : this.$el.querySelector(sel))
        : this.$el.querySelector(`[name="${steps[this.currentStep - 1]?.fields?.[0]}"]`);
      el?.focus();
    },

    // --- Polling fallback (if needed) ---
    _maybeStartPolling() {
      if (!enablePolling) return;
      setInterval(() => {
        // No-op: Alpine's $watch keeps validation up to date
      }, pollingInterval);
    },

    // --- Helpers for step text / title ---
    getStepTitle() {
      return steps[this.currentStep - 1]?.title || `Step ${this.currentStep}`;
    },
    getStepOfText() {
      return this.locales.step_of
        .replace('{current}', this.currentStep)
        .replace('{total}', totalSteps);
    },
    
  // --- Validation integration from x-validate-form ---
  _initValidation() {
    // Initialize state
    const inputs = this.$el.querySelectorAll('input[id], textarea[id], select[id]');
    inputs.forEach(input => {
      const name = input.name || input.id;
      this.errors[name] = false;
      this.errorMessages[name] = '';
      this.touchedFields[name] = false; // Initialize as not touched
      
      // Mark field as touched on first interaction
      const markAsTouched = () => {
        this.touchedFields[name] = true;
        // Enable errors for the step that this field belongs to
        const fieldStep = this._getFieldStep(name);
        this.stepErrorsEnabled[fieldStep] = true;
      };
      
      // On input change
      input.addEventListener('input', () => {
        markAsTouched();
        this._validateField(name, input);
        this._validateRelatedFields(name, input);
        // Hook after each field update
        if (typeof hooks.afterFieldUpdate === 'function') {
          const val = this.formData[name] ?? input.value;
          hooks.afterFieldUpdate(name, val, this);
        }
      });
      input.addEventListener('change', () => {
        markAsTouched();
        this._validateField(name, input);
        this._validateRelatedFields(name, input);
        // Group validation
        const container = input.closest('[data-group-name]');
        if (container) {
          const group = container.getAttribute('data-group-name');
          this._validateGroup(group, container);
        }
        // Hook after each field change
        if (typeof hooks.afterFieldUpdate === 'function') {
          const val = this.formData[name] ?? input.value;
          hooks.afterFieldUpdate(name, val, this);
        }
      });
      
      // Also mark as touched on focus for required fields (when user tabs/clicks)
      input.addEventListener('focus', () => {
        if (input.hasAttribute('required')) {
          markAsTouched();
        }
      });
    });
    this._initializeGroups();
  },

  _initializeGroups() {
    const containers = this.$el.querySelectorAll('[data-group-name]');
    containers.forEach(container => {
      const group = container.getAttribute('data-group-name');
      this.groupErrors[group] = '';
      this._validateGroup(group, container);
    });
  },

  _validateGroup(groupName, container) {
    // Reset
    this.groupErrors[groupName] = '';
    const inputs = Array.from(container.querySelectorAll('input[type="checkbox"]'));
    const count = inputs.filter(i => i.checked).length;
    const min = parseInt(container.getAttribute('data-group-min'), 10);
    const max = parseInt(container.getAttribute('data-group-max'), 10);
    const even = container.getAttribute('data-group-even') === 'true';
    const odd = container.getAttribute('data-group-odd') === 'true';
    const msg = container.getAttribute('data-group-error-message');
    if (!isNaN(min) && count < min) this.groupErrors[groupName] = msg || `Select at least ${min}`;
    if (!isNaN(max) && count > max) this.groupErrors[groupName] = msg || `Select no more than ${max}`;
    if (even && count % 2 !== 0) this.groupErrors[groupName] = msg || `Select an even number`;
    if (odd && count % 2 !== 1) this.groupErrors[groupName] = msg || `Select an odd number`;
  },

  _validateField(fieldName, input) {
    // Always update validation state internally
    const tempErrors = {};
    const tempErrorMessages = {};
    
    const value = this.formData[fieldName] ?? input.value;
    
    // Priority-based validation with early return
    // Priority 1: Required field validation (highest priority)
    if (input.hasAttribute('required') && !(value || '').trim()) {
      tempErrors[fieldName] = true;
      tempErrorMessages[fieldName] = input.getAttribute('data-required-message') || 'This field is required.';
    }
    // Priority 2: Format/pattern validation  
    else if (input.hasAttribute('pattern')) {
      const re = new RegExp(input.getAttribute('pattern'));
      if (!re.test(value)) {
        tempErrors[fieldName] = true;
        tempErrorMessages[fieldName] = input.getAttribute('data-pattern-message') || 'Invalid format.';
      }
    }
    // Priority 3: Length validation (min/max)
    else {
      const minL = parseInt(input.getAttribute('minlength'), 10);
      const maxL = parseInt(input.getAttribute('maxlength'), 10);
      if (!isNaN(minL) && value.length < minL) {
        tempErrors[fieldName] = true;
        tempErrorMessages[fieldName] = input.getAttribute('data-minlength-message') || `Minimum ${minL} characters.`;
      } else if (!isNaN(maxL) && value.length > maxL) {
        tempErrors[fieldName] = true;
        tempErrorMessages[fieldName] = input.getAttribute('data-maxlength-message') || `Maximum ${maxL} characters.`;
      }
    }
    
    // Find which step this field belongs to
    const fieldStep = this._getFieldStep(fieldName);
    
    // Only set visible errors if field has been touched AND step errors are enabled
    if (this.touchedFields[fieldName] && this.stepErrorsEnabled[fieldStep]) {
      this.errors[fieldName] = tempErrors[fieldName] || false;
      this.errorMessages[fieldName] = tempErrorMessages[fieldName] || '';
    } else {
      // Clear visible errors for untouched fields or steps with errors disabled
      this.errors[fieldName] = false;
      this.errorMessages[fieldName] = '';
    }
    
    // Related
    this._validateRelatedFields(fieldName, input);
  },

  _validateRelatedFields(fieldName, input) {
    const validateWith = input.getAttribute('data-validate-with');
    if (validateWith) {
      const other = this.$el.querySelector(`[name="${validateWith}"]`) || this.$el.querySelector(`#${validateWith}`);
      const otherVal = other ? (this.formData[validateWith] ?? other.value) : undefined;
      
      // Find which step this field belongs to
      const fieldStep = this._getFieldStep(fieldName);
      
      // Cross-field validation (e.g., password matching) - Priority 6 (after all other validations)
      // Only validate if the field doesn't already have an error from higher priority validations
      if (!this.errors[fieldName] && this.touchedFields[fieldName] && this.stepErrorsEnabled[fieldStep] && otherVal !== (this.formData[fieldName] ?? input.value)) {
        this.errors[fieldName] = true;
        this.errorMessages[fieldName] = input.getAttribute('data-related-error-message') || 'Fields do not match.';
      }
    }
  },    _beforeRequestHandler(e) {
      // Sync form data before submission
      this.syncFormData();
      
      // Validate all fields and groups before submit/navigation
      Object.keys(this.errors).forEach(name => {
        const input = this.$el.querySelector(`[name="${name}"]`) || this.$el.querySelector(`#${name}`);
        if (input) this._validateField(name, input);
      });
      this._initializeGroups();
    },
  };
}
