import multiStepForm from "./multiStepForm.js";

// Helper function to get next Monday or current date if today is Monday
function getDefaultStartDate() {
  const today = new Date();
  const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  
  if (currentDay === 1) {
    // Today is Monday, use today
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  } else {
    // Calculate next Monday
    const daysUntilMonday = currentDay === 0 ? 1 : (7 - currentDay + 1);
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + daysUntilMonday);
    
    const year = nextMonday.getFullYear();
    const month = String(nextMonday.getMonth() + 1).padStart(2, '0');
    const day = String(nextMonday.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
}

// Preconfigured multiStepNewSeasonForm Alpine.js directive for new season creation
export default function multiStepNewSeasonForm() {
  return multiStepForm({
    steps: [
      { title: 'Basic Information', fields: ['name', 'description'] },
      { title: 'Schedule Setup', fields: ['startDate'] },
      { title: 'Season Settings', fields: ['seasontype', 'frequency', 'amountOfTables', 'visibility'] },
      { title: 'Player Selection', fields: ['playerIds'] },
      { title: 'Review & Confirm', fields: [] },
    ],
    fields: [
      'name',
      'description',
      'startDate',
      'seasontype',
      'frequency',
      'amountOfTables',
      'visibility',
      'playerIds',
    ],
    fieldMapping: {},
    initialData: {
      visibility: 'public', // Set default value for visibility
      seasontype: '', // Start empty - user must make explicit selection
      frequency: '', // Start empty - user must make explicit selection
      amountOfTables: 1, // Set default value for amountOfTables
      playerIds: [], // Initialize as empty array for player selection
      startDate: getDefaultStartDate(), // Set default start date to next Monday (or current date if today is Monday)
    },
    validate: [
      // Step 1: Basic Info - require only name, description is optional
      (formData) => formData.name.trim() !== '',
      // Step 2: Dates - only require startDate
      (formData) => {
        if (!formData.startDate) return false;
        const start = new Date(formData.startDate);
        return start instanceof Date && !isNaN(start);
      },
      // Step 3: Settings
      (formData) => {
        return ['pool', 'bowling', 'other'].includes(formData.seasontype) &&
               ['weekly', 'biweekly', 'monthly', 'quarterly', 'yearly'].includes(formData.frequency) &&
               formData.amountOfTables >= 1 &&
               ['public', 'private'].includes(formData.visibility);
      },
      // Step 4: Player Selection
      (formData) => formData.playerIds && formData.playerIds.length >= 2,
      // Step 5: Review
      () => true,
    ],
    focusSelectors: [
      '[name="name"]',
      '[name="startDate"]',
      '[name="seasontype"]',
      '[name="playerIds[]"]',
      null,
    ],
    locales: {
      step_of: 'Step {current} of {total}',
    },
    submitOnEnter: true,
    pollingInterval: 200,
    enablePolling: true,
    hooks: {},
  });
}
